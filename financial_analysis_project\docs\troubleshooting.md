# 🔧 Troubleshooting Guide

Common issues and solutions for the Financial Analysis Project.

## 🚨 Common Issues

### 1. Installation Problems

#### Issue: Python Version Error
```
Error: Python 3.8 or higher is required
Current version: Python 3.7.x
```

**Solutions:**
1. **Update Python:**
   ```bash
   # Download from python.org or use package manager
   # Windows: Download from python.org
   # macOS: brew install python@3.9
   # Ubuntu: sudo apt install python3.9
   ```

2. **Use pyenv (Recommended):**
   ```bash
   # Install pyenv
   curl https://pyenv.run | bash
   
   # Install Python 3.9
   pyenv install 3.9.16
   pyenv local 3.9.16
   ```

#### Issue: Dependency Installation Fails
```
ERROR: Could not install packages due to an EnvironmentError
```

**Solutions:**
1. **Use Virtual Environment:**
   ```bash
   python -m venv financial_env
   source financial_env/bin/activate  # Linux/Mac
   financial_env\Scripts\activate     # Windows
   pip install -r requirements.txt
   ```

2. **Update pip:**
   ```bash
   python -m pip install --upgrade pip
   ```

3. **Clear pip cache:**
   ```bash
   pip cache purge
   pip install -r requirements.txt
   ```

### 2. Environment Configuration

#### Issue: GROQ API Key Not Found
```
Error: GROQ_API_KEY not found in environment
```

**Solutions:**
1. **Check .env file exists:**
   ```bash
   ls backend/.env
   ```

2. **Create .env from template:**
   ```bash
   cd backend
   cp .env.example .env
   ```

3. **Add your API key:**
   ```bash
   # Edit backend/.env
   GROQ_API_KEY=your_actual_api_key_here
   ```

4. **Get API key:**
   - Visit https://console.groq.com/
   - Sign up for free account
   - Generate API key
   - Copy to .env file

#### Issue: Environment Variables Not Loading
```
Error: Configuration not found
```

**Solutions:**
1. **Check file location:**
   ```bash
   # .env should be in backend/ directory
   backend/.env
   ```

2. **Verify file content:**
   ```bash
   cat backend/.env
   # Should show: GROQ_API_KEY=your_key_here
   ```

3. **Restart application:**
   ```bash
   # Stop and restart both services
   Ctrl+C
   python setup_and_run.py
   ```

### 3. Service Startup Issues

#### Issue: Backend Won't Start
```
Error: Port 8000 is already in use
```

**Solutions:**
1. **Kill existing process:**
   ```bash
   # Find process using port 8000
   lsof -ti:8000
   
   # Kill the process
   lsof -ti:8000 | xargs kill -9
   ```

2. **Use different port:**
   ```bash
   cd backend
   uvicorn app.main:app --port 8001
   ```

3. **Check for other applications:**
   - Stop other development servers
   - Check Docker containers
   - Restart your computer if needed

#### Issue: Frontend Connection Error
```
Error: Cannot connect to backend API
```

**Solutions:**
1. **Verify backend is running:**
   ```bash
   curl http://localhost:8000/health
   # Should return: {"status": "healthy"}
   ```

2. **Check backend logs:**
   ```bash
   # Look for errors in backend terminal
   # Common issues: missing dependencies, API key problems
   ```

3. **Verify ports:**
   ```bash
   # Backend should be on 8000
   # Frontend should be on 8501
   netstat -an | grep 8000
   netstat -an | grep 8501
   ```

### 4. File Upload Issues

#### Issue: File Format Not Supported
```
Error: Unsupported file type
```

**Solutions:**
1. **Check file extension:**
   - Supported: .xlsx, .xls, .csv
   - Not supported: .xlsm, .xlsb, .ods

2. **Convert file format:**
   ```bash
   # Open in Excel and save as .xlsx
   # Or use online converter
   ```

3. **Verify file integrity:**
   - Open file in Excel to ensure it's not corrupted
   - Check for password protection

#### Issue: File Too Large
```
Error: File too large. Maximum size: 50MB
```

**Solutions:**
1. **Reduce file size:**
   - Remove unnecessary columns
   - Filter to essential data
   - Split into multiple files

2. **Use CSV format:**
   ```bash
   # CSV files are typically smaller
   # Save Excel as CSV
   ```

3. **Increase limit (if needed):**
   ```python
   # In backend/app/main.py
   MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
   ```

#### Issue: Missing Required Columns
```
Error: Missing required columns: ['supplier']
```

**Solutions:**
1. **Check column names:**
   ```
   Required columns (flexible matching):
   - supplier: supplier, vendor, proveedor, supplier_name
   - voucher: voucher, invoice, factura, comprobante
   - gross_amount: amount, total, importe, monto
   ```

2. **Rename columns:**
   - Open Excel file
   - Rename columns to match expected names
   - Save and re-upload

3. **Check data format:**
   - Ensure data starts from row 1
   - Remove merged cells
   - Remove summary rows

### 5. Analysis Issues

#### Issue: Analysis Takes Too Long
```
Analysis running for more than 5 minutes
```

**Solutions:**
1. **Check file size:**
   - Large files take longer
   - Try with smaller sample first

2. **Check internet connection:**
   - API calls to GROQ require internet
   - Slow connection affects performance

3. **Monitor system resources:**
   ```bash
   # Check memory usage
   top
   # Check disk space
   df -h
   ```

4. **Restart services:**
   ```bash
   # Stop and restart
   Ctrl+C
   python setup_and_run.py
   ```

#### Issue: Analysis Fails with Error
```
Error: Analysis failed
```

**Solutions:**
1. **Check backend logs:**
   - Look for specific error messages
   - Common issues: data format, API limits

2. **Verify data quality:**
   - Check for special characters
   - Ensure numeric columns contain numbers
   - Remove empty rows

3. **Try with sample data:**
   ```bash
   # Test with provided sample files
   sample_data/sample_financial_data.xlsx
   ```

### 6. Performance Issues

#### Issue: Slow Response Times
```
Analysis taking longer than expected
```

**Solutions:**
1. **Optimize data:**
   - Remove unnecessary columns
   - Filter date ranges
   - Clean data before upload

2. **Check system resources:**
   ```bash
   # Monitor CPU and memory
   htop  # Linux/Mac
   # Task Manager on Windows
   ```

3. **Close other applications:**
   - Free up system resources
   - Close browser tabs
   - Stop other development servers

#### Issue: Memory Errors
```
MemoryError: Unable to allocate array
```

**Solutions:**
1. **Reduce file size:**
   - Process data in smaller chunks
   - Remove unnecessary columns

2. **Increase system memory:**
   - Close other applications
   - Restart computer
   - Consider upgrading RAM

3. **Use CSV format:**
   - More memory efficient than Excel
   - Faster processing

## 🔍 Debugging Steps

### 1. Check Service Status
```bash
# Backend health check
curl http://localhost:8000/health

# Frontend access
curl http://localhost:8501
```

### 2. Review Logs
```bash
# Backend logs (in terminal running uvicorn)
# Look for error messages, stack traces

# Frontend logs (in terminal running streamlit)
# Check for connection errors
```

### 3. Test with Sample Data
```bash
# Use provided sample files
cd sample_data
python create_sample_data.py

# Test with sample_financial_data.xlsx
```

### 4. Verify Configuration
```bash
# Check environment variables
cat backend/.env

# Verify Python version
python --version

# Check installed packages
pip list
```

## 📞 Getting Help

### 1. Self-Help Resources
- [FAQ](faq.md) - Frequently asked questions
- [Installation Guide](installation.md) - Detailed setup instructions
- [API Documentation](api-documentation.md) - API reference

### 2. Diagnostic Information
When reporting issues, include:
- Operating system and version
- Python version
- Error messages (full stack trace)
- Steps to reproduce
- File size and format
- Configuration settings

### 3. Common Error Patterns

#### Pattern: Import Errors
```python
ModuleNotFoundError: No module named 'langchain'
```
**Solution**: Reinstall dependencies in virtual environment

#### Pattern: API Errors
```json
{"error": "API key invalid"}
```
**Solution**: Check GROQ API key configuration

#### Pattern: Data Errors
```
ValueError: could not convert string to float
```
**Solution**: Clean data, remove non-numeric values

## 🛠️ Advanced Troubleshooting

### 1. Debug Mode
```bash
# Enable debug logging
export DEBUG=True

# Run with verbose output
uvicorn app.main:app --reload --log-level debug
```

### 2. Test Individual Components
```python
# Test Excel processor
from app.services.excel_processor import ExcelProcessor
processor = ExcelProcessor()

# Test API endpoints
import requests
response = requests.get('http://localhost:8000/health')
```

### 3. Reset Environment
```bash
# Complete reset
rm -rf financial_analysis_env
python -m venv financial_analysis_env
source financial_analysis_env/bin/activate
pip install -r backend/requirements.txt
pip install -r frontend/requirements.txt
```

---

**Still having issues?** Check the [FAQ](faq.md) or create an issue in the project repository with detailed information about your problem.
