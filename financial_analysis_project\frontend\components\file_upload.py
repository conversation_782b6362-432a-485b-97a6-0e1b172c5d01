"""
File upload component for Streamlit frontend.
"""

import streamlit as st
import pandas as pd
from typing import Optional, List
import io


class FileUploadComponent:
    """Component for handling file uploads and validation."""
    
    def __init__(self, supported_formats: List[str] = None):
        self.supported_formats = supported_formats or ['.xlsx', '.xls', '.csv']
    
    def render_upload_area(self, key: str = "file_upload") -> Optional[st.runtime.uploaded_file_manager.UploadedFile]:
        """
        Render file upload area with validation.
        
        Args:
            key: Unique key for the file uploader
            
        Returns:
            Uploaded file object or None
        """
        st.markdown("### 📁 Upload Financial Data")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose an Excel or CSV file",
            type=[fmt.lstrip('.') for fmt in self.supported_formats],
            key=key,
            help=f"Supported formats: {', '.join(self.supported_formats)}"
        )
        
        if uploaded_file is not None:
            self._display_file_info(uploaded_file)
            
            # Preview data
            if st.checkbox("Preview data", key=f"{key}_preview"):
                self._preview_file_data(uploaded_file)
        
        return uploaded_file
    
    def _display_file_info(self, uploaded_file):
        """Display information about the uploaded file."""
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("File Name", uploaded_file.name)
        
        with col2:
            file_size_mb = uploaded_file.size / (1024 * 1024)
            st.metric("File Size", f"{file_size_mb:.2f} MB")
        
        with col3:
            file_type = uploaded_file.type
            st.metric("File Type", file_type)
    
    def _preview_file_data(self, uploaded_file):
        """Preview the uploaded file data."""
        try:
            # Read file based on type
            if uploaded_file.name.endswith('.csv'):
                df = pd.read_csv(uploaded_file)
            else:
                df = pd.read_excel(uploaded_file)
            
            st.subheader("📊 Data Preview")
            
            # Show basic info
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Rows", len(df))
            with col2:
                st.metric("Columns", len(df.columns))
            with col3:
                st.metric("Memory Usage", f"{df.memory_usage(deep=True).sum() / 1024:.1f} KB")
            
            # Show column info
            st.subheader("Column Information")
            column_info = pd.DataFrame({
                'Column': df.columns,
                'Data Type': df.dtypes.astype(str),
                'Non-Null Count': df.count(),
                'Null Count': df.isnull().sum(),
                'Sample Values': [str(df[col].dropna().iloc[0]) if not df[col].dropna().empty else 'N/A' for col in df.columns]
            })
            st.dataframe(column_info, use_container_width=True)
            
            # Show first few rows
            st.subheader("First 5 Rows")
            st.dataframe(df.head(), use_container_width=True)
            
            # Data quality checks
            self._show_data_quality_checks(df)
            
        except Exception as e:
            st.error(f"Error previewing file: {str(e)}")
    
    def _show_data_quality_checks(self, df: pd.DataFrame):
        """Show data quality checks."""
        st.subheader("🔍 Data Quality Checks")
        
        # Check for required columns (flexible matching)
        required_patterns = {
            'Supplier': ['supplier', 'vendor', 'proveedor', 'supplier_name', 'vendor_name'],
            'Voucher': ['voucher', 'invoice', 'factura', 'comprobante', 'voucher_id', 'invoice_id'],
            'Amount': ['amount', 'gross_amount', 'total', 'importe', 'monto', 'gross']
        }
        
        found_columns = {}
        for category, patterns in required_patterns.items():
            found = []
            for col in df.columns:
                if any(pattern.lower() in col.lower() for pattern in patterns):
                    found.append(col)
            found_columns[category] = found
        
        # Display findings
        for category, columns in found_columns.items():
            if columns:
                st.success(f"✅ {category} columns found: {', '.join(columns)}")
            else:
                st.warning(f"⚠️ No {category} columns detected")
        
        # Check for missing values
        missing_data = df.isnull().sum()
        if missing_data.sum() > 0:
            st.warning("⚠️ Missing values detected:")
            missing_df = pd.DataFrame({
                'Column': missing_data.index,
                'Missing Count': missing_data.values,
                'Missing %': (missing_data.values / len(df) * 100).round(2)
            })
            missing_df = missing_df[missing_df['Missing Count'] > 0]
            st.dataframe(missing_df, use_container_width=True)
        else:
            st.success("✅ No missing values detected")
        
        # Check for duplicates
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            st.warning(f"⚠️ {duplicates} duplicate rows detected")
        else:
            st.success("✅ No duplicate rows detected")
    
    def validate_file_format(self, uploaded_file) -> tuple[bool, str]:
        """
        Validate uploaded file format.
        
        Args:
            uploaded_file: Streamlit uploaded file object
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if uploaded_file is None:
            return False, "No file uploaded"
        
        file_extension = '.' + uploaded_file.name.split('.')[-1].lower()
        
        if file_extension not in self.supported_formats:
            return False, f"Unsupported file format. Supported: {', '.join(self.supported_formats)}"
        
        # Check file size (50MB limit)
        max_size = 50 * 1024 * 1024  # 50MB
        if uploaded_file.size > max_size:
            return False, f"File too large. Maximum size: {max_size / (1024*1024):.0f}MB"
        
        return True, "File format is valid"
    
    def render_upload_instructions(self):
        """Render instructions for file upload."""
        st.markdown("""
        ### 📋 File Upload Instructions
        
        **Required Columns:**
        - **Supplier/Vendor**: Names of suppliers (e.g., 'supplier', 'vendor', 'proveedor')
        - **Voucher/Invoice**: Transaction identifiers (e.g., 'voucher', 'invoice', 'factura')
        - **Amount**: Gross amounts (e.g., 'gross_amount', 'amount', 'total', 'importe')
        
        **Optional Columns:**
        - **Cost**: Cost amounts (e.g., 'cost', 'cost_amount', 'costo')
        - **Date**: Transaction dates (e.g., 'date', 'transaction_date', 'fecha')
        
        **File Requirements:**
        - Formats: Excel (.xlsx, .xls) or CSV (.csv)
        - Maximum size: 50MB
        - Data should be in tabular format with headers
        
        **Tips:**
        - Ensure column names are descriptive
        - Remove any summary rows or merged cells
        - Use consistent date formats
        - Numeric amounts should not contain currency symbols
        """)


def create_sample_data_download():
    """Create a sample data file for download."""
    sample_data = pd.DataFrame({
        'supplier': ['ABC Corp', 'XYZ Ltd', 'Global Inc', 'Local Co', 'Tech Solutions'],
        'voucher': ['INV-001', 'INV-002', 'INV-003', 'INV-004', 'INV-005'],
        'gross_amount': [10000, 15000, 8000, 12000, 20000],
        'cost': [7000, 10500, 6400, 8400, 14000],
        'date': pd.date_range('2024-01-01', periods=5, freq='D')
    })
    
    # Convert to Excel bytes
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        sample_data.to_excel(writer, index=False, sheet_name='Financial_Data')
    
    return output.getvalue()
