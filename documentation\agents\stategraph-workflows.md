# StateGraph Workflows

This document provides a deep dive into the LangGraph StateGraph implementation used in both the Financial Analysis and Stock Analysis systems.

## Overview

Both systems use **LangGraph's StateGraph** to orchestrate complex multi-agent workflows. StateGraph provides:
- **State Management**: Shared state across all agents
- **Workflow Orchestration**: Defined execution paths
- **Parallel Processing**: Concurrent agent execution
- **Error Handling**: Robust error recovery mechanisms

## Core Concepts

### State Management
StateGraph uses TypedDict classes to define shared state that persists across all workflow nodes:

```python
from typing import TypedDict, Annotated, List
import operator

class WorkflowState(TypedDict):
    # Annotated lists that accumulate results
    data: Annotated[List[Any], operator.add]
    results: Annotated[List[str], operator.add]
    errors: Annotated[List[str], operator.add]
```

### Graph Construction
```python
from langgraph.graph import StateGraph, START, END

# Create graph with state type
graph = StateGraph(WorkflowState)

# Add nodes (functions that process state)
graph.add_node("process_data", process_data_function)
graph.add_node("analyze", analyze_function)

# Define execution flow
graph.add_edge(START, "process_data")
graph.add_edge("process_data", "analyze")
graph.add_edge("analyze", END)

# Compile the workflow
app = graph.compile()
```

## Financial Analysis Workflow

### State Definition
```python
class FinancialAnalysisState(TypedDict):
    raw_data: Annotated[List[pd.DataFrame], operator.add]
    processed_data: Annotated[List[pd.DataFrame], operator.add]
    supplier_analysis: Annotated[List[str], operator.add]
    voucher_analysis: Annotated[List[str], operator.add]
    margin_analysis: Annotated[List[str], operator.add]
    trend_analysis: Annotated[List[str], operator.add]
    final_report: Annotated[List[str], operator.add]
    assumptions: Annotated[List[str], operator.add]
    warnings: Annotated[List[str], operator.add]
```

### Workflow Graph
```python
financial_analysis_graph = StateGraph(FinancialAnalysisState)

# Add processing nodes
financial_analysis_graph.add_node("process_data", process_excel_data)
financial_analysis_graph.add_node("analyze_suppliers", analyze_supplier_amounts)
financial_analysis_graph.add_node("analyze_vouchers", analyze_voucher_amounts)
financial_analysis_graph.add_node("calculate_margins", calculate_margins)
financial_analysis_graph.add_node("analyze_trends", analyze_trends)
financial_analysis_graph.add_node("generate_report", generate_final_report)

# Define execution flow
financial_analysis_graph.add_edge(START, "process_data")
financial_analysis_graph.add_edge("process_data", "analyze_suppliers")
financial_analysis_graph.add_edge("process_data", "analyze_vouchers")
financial_analysis_graph.add_edge("analyze_suppliers", "calculate_margins")
financial_analysis_graph.add_edge("analyze_vouchers", "calculate_margins")
financial_analysis_graph.add_edge("calculate_margins", "analyze_trends")
financial_analysis_graph.add_edge("analyze_trends", "generate_report")
financial_analysis_graph.add_edge("generate_report", END)
```

### Parallel Processing
The workflow processes suppliers and vouchers in parallel:
```
process_data
    ├── analyze_suppliers ──┐
    └── analyze_vouchers ───┤
                           ├── calculate_margins
```

## Stock Analysis Workflow

### State Definition
```python
class Estado(TypedDict):
    consulta: Annotated[List[str], operator.add]
    ticker: Annotated[List[str], operator.add]
    datos_financieros: Annotated[List[pd.DataFrame], operator.add]
    respuesta_analisis: Annotated[List[str], operator.add]
    ruta_html: Annotated[List[str], operator.add]
    noticias: Annotated[List[str], operator.add]
    respuesta_final: Annotated[List[str], operator.add]
```

### Workflow Graph
```python
grafico = StateGraph(Estado)

# Add processing nodes
grafico.add_node("extraer_ticker", extraer_ticker)
grafico.add_node("obtener_datos_financieros", obtener_datos_financieros)
grafico.add_node("analizar_datos", analizar_datos)
grafico.add_node("obtener_noticias", obtener_noticias)
grafico.add_node("analista_financiero", analista_financiero)

# Define execution flow with synchronization
grafico.add_edge(START, "extraer_ticker")
grafico.add_edge("extraer_ticker", "obtener_datos_financieros")
grafico.add_edge("extraer_ticker", "obtener_noticias")
grafico.add_edge("obtener_datos_financieros", "analizar_datos")

# Synchronization node
grafico.add_node("esperar_ambos", lambda x: x)
grafico.add_edge("analizar_datos", "esperar_ambos")
grafico.add_edge("obtener_noticias", "esperar_ambos")
grafico.add_edge("esperar_ambos", "analista_financiero")
grafico.add_edge("analista_financiero", END)
```

### Synchronization Pattern
The stock analysis uses explicit synchronization:
```
extraer_ticker
    ├── obtener_datos_financieros → analizar_datos ──┐
    └── obtener_noticias ─────────────────────────────┤
                                                     ├── esperar_ambos → analista_financiero
```

## Node Implementation Patterns

### Basic Node Function
```python
def process_node(state: WorkflowState) -> WorkflowState:
    """Process state and return updated state."""
    # Get input data
    input_data = state["input_field"][-1] if state["input_field"] else None
    
    # Process data
    result = perform_processing(input_data)
    
    # Update state
    state["output_field"].append(result)
    
    return state
```

### Agent Integration Node
```python
def agent_node(state: WorkflowState) -> WorkflowState:
    """Node that uses an AI agent."""
    # Initialize agent
    agent = SpecializedAgent()
    
    # Get input from state
    input_data = state["processed_data"][-1]
    
    # Execute agent
    result = agent.execute(input_data)
    
    # Store result in state
    state["agent_results"].append(result)
    
    return state
```

### Error Handling Node
```python
def safe_processing_node(state: WorkflowState) -> WorkflowState:
    """Node with error handling."""
    try:
        # Attempt processing
        result = risky_operation(state["input"][-1])
        state["results"].append(result)
    except Exception as e:
        # Handle error gracefully
        error_msg = f"Processing failed: {str(e)}"
        state["errors"].append(error_msg)
        state["results"].append("Error occurred during processing")
    
    return state
```

## Advanced Patterns

### Conditional Routing
```python
def conditional_node(state: WorkflowState) -> str:
    """Return next node based on state."""
    if state["data_quality"][-1] > 0.8:
        return "high_quality_processing"
    else:
        return "data_cleaning"

# Add conditional routing
graph.add_conditional_edges(
    "data_validation",
    conditional_node,
    {
        "high_quality_processing": "analyze",
        "data_cleaning": "clean_data"
    }
)
```

### Loop Patterns
```python
def should_continue(state: WorkflowState) -> str:
    """Determine if processing should continue."""
    if state["iterations"][-1] < 3 and state["quality"][-1] < 0.9:
        return "continue"
    else:
        return "finish"

# Add loop
graph.add_conditional_edges(
    "iterative_process",
    should_continue,
    {
        "continue": "iterative_process",  # Loop back
        "finish": "final_output"
    }
)
```

## State Management Best Practices

### 1. Use Annotated Lists
```python
# Good: Accumulates results
field: Annotated[List[str], operator.add]

# Avoid: Overwrites previous values
field: str
```

### 2. Access Latest Values
```python
# Get the most recent value
latest_result = state["results"][-1] if state["results"] else None
```

### 3. Handle Empty States
```python
def safe_node(state: WorkflowState) -> WorkflowState:
    if not state["input_data"]:
        state["errors"].append("No input data provided")
        return state
    
    # Process with confidence
    data = state["input_data"][-1]
    # ... processing logic
```

### 4. State Validation
```python
def validate_state(state: WorkflowState) -> WorkflowState:
    """Validate state before processing."""
    required_fields = ["raw_data", "config"]
    
    for field in required_fields:
        if not state.get(field):
            state["errors"].append(f"Missing required field: {field}")
    
    return state
```

## Execution and Monitoring

### Basic Execution
```python
# Initialize state
initial_state = {
    "input_data": [your_data],
    "results": [],
    "errors": []
}

# Execute workflow
final_state = app.invoke(initial_state)

# Get results
results = final_state["results"]
errors = final_state["errors"]
```

### Streaming Execution
```python
# Stream results as they become available
for chunk in app.stream(initial_state):
    print(f"Step completed: {chunk}")
```

### Debug Mode
```python
# Enable verbose logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Execute with detailed output
result = app.invoke(initial_state)
```

## Performance Optimization

### 1. Minimize State Size
- Store only necessary data in state
- Use references instead of copying large objects
- Clean up intermediate results when no longer needed

### 2. Optimize Parallel Paths
- Identify independent operations
- Use parallel edges effectively
- Balance workload across parallel branches

### 3. Error Recovery
- Implement graceful degradation
- Provide fallback mechanisms
- Log errors for debugging

## Next Steps

- [Agent Architecture](./agent-architecture.md) - Overall agent design patterns
- [Financial Analysis Agents](./financial-analysis-agents.md) - Specific implementation details
- [Stock Analysis Agents](./stock-analysis-agents.md) - Stock workflow specifics
