# Project Documentation

Welcome to the comprehensive documentation for the **Arroyo Consulting Financial Analysis Platform**.

This project is an AI-powered financial analysis system built with Lang<PERSON>hai<PERSON> and LangGraph that processes Excel financial data using specialized AI agents.

## 🏗️ Project Structure

```
├── financial_analysis_project/     # Main Excel financial analysis system
├── documentation/                 # This documentation folder
├── sample_data/                  # Sample data files
└── ejemplo.py                     # Example workflow (for reference only)
```

## 📊 Main System

### Financial Analysis Project
- **Purpose**: Analyze Excel financial data using specialized AI agents
- **Technology**: FastAPI backend, Streamlit frontend, LangGraph workflows
- **Key Features**: Supplier analysis, margin calculations, trend analysis, comprehensive reporting

## 📚 Documentation Index

### Getting Started
- [Quick Start Guide](./quick-start.md) - Get up and running in 5 minutes
- [Installation Guide](./installation.md) - Detailed setup instructions
- [Configuration](./configuration.md) - Environment variables and settings

### Agent Workflows
- [Financial Analysis Agents](./agents/financial-analysis-agents.md) - Excel data processing workflow
- [Agent Architecture](./agents/agent-architecture.md) - How agents work together
- [StateGraph Workflows](./agents/stategraph-workflows.md) - LangGraph implementation details

### Technical Documentation
- [System Architecture](./technical/architecture.md) - Overall system design
- [API Reference](./technical/api-reference.md) - REST API documentation
- [Database Schema](./technical/database-schema.md) - Data structures
- [Security](./technical/security.md) - Security considerations

### User Guides
- [Financial Analysis Tutorial](./tutorials/financial-analysis.md) - Step-by-step Excel analysis
- [Advanced Features](./tutorials/advanced-features.md) - Power user features

### Development
- [Development Setup](./development/setup.md) - Developer environment
- [Contributing](./development/contributing.md) - How to contribute
- [Testing](./development/testing.md) - Running tests
- [Deployment](./development/deployment.md) - Production deployment

### Reference
- [Troubleshooting](./reference/troubleshooting.md) - Common issues and solutions
- [FAQ](./reference/faq.md) - Frequently asked questions
- [Glossary](./reference/glossary.md) - Technical terms and definitions

## 🚀 Quick Links

- **Start Here**: [Quick Start Guide](./quick-start.md)
- **For Developers**: [Development Setup](./development/setup.md)
- **For Users**: [Financial Analysis Tutorial](./tutorials/financial-analysis.md)
- **Need Help?**: [Troubleshooting](./reference/troubleshooting.md)

## 🔧 Technology Stack

- **AI/ML**: LangChain, LangGraph, ChatGroq
- **Backend**: FastAPI, Python
- **Frontend**: Streamlit
- **Data Processing**: Pandas, OpenPyXL
- **Containerization**: Docker, Docker Compose

## 📝 Last Updated

This documentation was last updated on: **2025-01-08**

---

**Need help?** Check our [FAQ](./reference/faq.md) or [Troubleshooting Guide](./reference/troubleshooting.md).
