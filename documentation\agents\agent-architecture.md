# Agent Architecture

This document explains the overall architecture and design patterns used in the Financial Analysis Platform's AI agent system.

## Overview

The Financial Analysis Platform uses a **multi-agent architecture** built on <PERSON><PERSON><PERSON><PERSON> and LangGraph to process Excel financial data. The system orchestrates multiple specialized AI agents that work together to provide comprehensive financial analysis.

## Core Architecture Principles

### 1. Separation of Concerns
Each agent has a specific responsibility:
- **Data Processing**: Excel validation and cleaning
- **Financial Analysis**: Calculations and metrics
- **Trend Analysis**: Temporal pattern recognition
- **Report Generation**: Synthesis and presentation

### 2. State-Driven Workflow
All agents share a common state that accumulates results:
```python
class FinancialAnalysisState(TypedDict):
    raw_data: Annotated[List[pd.DataFrame], operator.add]
    processed_data: Annotated[List[pd.DataFrame], operator.add]
    supplier_analysis: Annotated[List[str], operator.add]
    voucher_analysis: Annotated[List[str], operator.add]
    margin_analysis: Annotated[List[str], operator.add]
    trend_analysis: Annotated[List[str], operator.add]
    final_report: Annotated[List[str], operator.add]
    assumptions: Annotated[List[str], operator.add]
    warnings: Annotated[List[str], operator.add]
```

### 3. Parallel Processing
Independent analyses run concurrently:
- Supplier analysis and voucher analysis execute in parallel
- Results are synchronized before margin calculation
- Maximizes throughput and minimizes processing time

## Agent Design Patterns

### 1. Specialized Agent Pattern
Each agent is designed for a specific domain:

```python
class SpecializedAgent:
    def __init__(self):
        self.llm = ChatGroq(model="appropriate_model")
        self.prompt = PromptTemplate(...)
        self.chain = self.prompt | self.llm | StrOutputParser()
    
    def execute(self, input_data):
        return self.chain.invoke(input_data)
```

### 2. Pandas Agent Pattern
For complex data analysis tasks:

```python
class DataAnalysisAgent:
    def __init__(self):
        self.llm = ChatGroq(model="llama3-70b-8192")
    
    def execute(self, df: pd.DataFrame) -> str:
        agent = create_pandas_dataframe_agent(
            llm=self.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True
        )
        return agent.invoke({"input": query})
```

### 3. Synthesis Agent Pattern
For combining multiple inputs:

```python
class SynthesisAgent:
    def __init__(self):
        self.llm = ChatGroq(model="gemma2-9b-it")
        self.prompt = PromptTemplate(
            input_variables=["analysis1", "analysis2", "analysis3"],
            template="Synthesize the following analyses..."
        )
    
    def generate(self, **analyses):
        return self.chain.invoke(analyses)
```

## Workflow Orchestration

### StateGraph Implementation
```python
from langgraph.graph import StateGraph, START, END

# Create workflow graph
financial_analysis_graph = StateGraph(FinancialAnalysisState)

# Add agent nodes
financial_analysis_graph.add_node("process_data", process_excel_data)
financial_analysis_graph.add_node("analyze_suppliers", analyze_supplier_amounts)
financial_analysis_graph.add_node("analyze_vouchers", analyze_voucher_amounts)
financial_analysis_graph.add_node("calculate_margins", calculate_margins)
financial_analysis_graph.add_node("analyze_trends", analyze_trends)
financial_analysis_graph.add_node("generate_report", generate_final_report)

# Define execution flow
financial_analysis_graph.add_edge(START, "process_data")
financial_analysis_graph.add_edge("process_data", "analyze_suppliers")
financial_analysis_graph.add_edge("process_data", "analyze_vouchers")
financial_analysis_graph.add_edge("analyze_suppliers", "calculate_margins")
financial_analysis_graph.add_edge("analyze_vouchers", "calculate_margins")
financial_analysis_graph.add_edge("calculate_margins", "analyze_trends")
financial_analysis_graph.add_edge("analyze_trends", "generate_report")
financial_analysis_graph.add_edge("generate_report", END)

# Compile workflow
financial_analysis_app = financial_analysis_graph.compile()
```

### Execution Flow
```mermaid
graph TD
    Start([START]) --> Process[Process Excel Data]
    Process --> Supplier[Analyze Suppliers]
    Process --> Voucher[Analyze Vouchers]
    Supplier --> Margin[Calculate Margins]
    Voucher --> Margin
    Margin --> Trend[Analyze Trends]
    Trend --> Report[Generate Report]
    Report --> End([END])
```

## Agent Specializations

### 1. ExcelProcessorAgent
**Purpose**: Data validation and preprocessing
**Model**: `llama-3.1-8b-instant` (fast, efficient)
**Capabilities**:
- Column detection and validation
- Data type inference
- Quality assessment
- Cleaning recommendations

**Design Rationale**: Uses a fast model for quick data validation tasks that don't require complex reasoning.

### 2. GrossAmountAnalyzer
**Purpose**: Financial calculations and aggregations
**Model**: `llama3-70b-8192` (high accuracy)
**Capabilities**:
- Supplier-level aggregations
- Voucher-level analysis
- Statistical summaries
- Performance rankings

**Design Rationale**: Uses a powerful model for accurate financial calculations and complex aggregations.

### 3. MarginAnalyzer
**Purpose**: Profitability analysis and risk assessment
**Model**: `llama3-70b-8192` (high accuracy)
**Capabilities**:
- Margin calculations
- Risk identification
- Threshold analysis
- Cost estimation

**Design Rationale**: Critical financial analysis requires high accuracy and sophisticated reasoning.

### 4. TrendAnalyzer
**Purpose**: Temporal pattern analysis
**Model**: `llama3-70b-8192` (high accuracy)
**Capabilities**:
- Time series analysis
- Trend identification
- Seasonal patterns
- Growth calculations

**Design Rationale**: Complex temporal analysis benefits from advanced reasoning capabilities.

### 5. ReportGenerator
**Purpose**: Synthesis and presentation
**Model**: `gemma2-9b-it` (creative, comprehensive)
**Capabilities**:
- Multi-source synthesis
- Professional formatting
- Executive summaries
- Actionable insights

**Design Rationale**: Report generation requires creativity and strong language capabilities.

## Error Handling and Resilience

### 1. Graceful Degradation
```python
def safe_agent_execution(agent, input_data, fallback_result="Analysis unavailable"):
    try:
        return agent.execute(input_data)
    except Exception as e:
        logging.error(f"Agent execution failed: {e}")
        return fallback_result
```

### 2. State Validation
```python
def validate_state(state: FinancialAnalysisState) -> bool:
    required_fields = ["raw_data", "processed_data"]
    for field in required_fields:
        if not state.get(field):
            return False
    return True
```

### 3. Retry Mechanisms
```python
def execute_with_retry(agent, input_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            return agent.execute(input_data)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # Exponential backoff
```

## Performance Optimization

### 1. Model Selection Strategy
- **Fast Models**: Quick validation and preprocessing tasks
- **Accurate Models**: Critical financial calculations
- **Creative Models**: Report generation and synthesis

### 2. Parallel Execution
- Independent analyses run concurrently
- Synchronization points minimize waiting
- Resource utilization optimization

### 3. Caching Strategy
```python
@functools.lru_cache(maxsize=100)
def cached_analysis(data_hash, analysis_type):
    # Cache expensive operations
    pass
```

## Extensibility Patterns

### Adding New Agents
1. **Create Agent Class**: Implement the agent interface
2. **Add to StateGraph**: Include in workflow definition
3. **Update State**: Add new fields if needed
4. **Define Edges**: Specify execution dependencies

### Custom Analysis Types
```python
class CustomAnalysisAgent:
    def __init__(self, custom_prompt):
        self.llm = ChatGroq(model="llama3-70b-8192")
        self.prompt = PromptTemplate(template=custom_prompt)
        self.chain = self.prompt | self.llm | StrOutputParser()
    
    def execute(self, df: pd.DataFrame) -> str:
        # Custom analysis logic
        pass
```

### Integration Points
- **External APIs**: Add new data sources
- **Custom Models**: Integrate different LLM providers
- **Output Formats**: Support additional export formats
- **Business Rules**: Implement domain-specific logic

## Monitoring and Observability

### 1. Execution Tracking
```python
def track_execution(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            log_success(func.__name__, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_error(func.__name__, duration, str(e))
            raise
    return wrapper
```

### 2. State Monitoring
- Track state transitions
- Monitor data quality metrics
- Alert on anomalies
- Performance profiling

### 3. Agent Performance Metrics
- Execution time per agent
- Success/failure rates
- Resource utilization
- Output quality scores

## Best Practices

### 1. Agent Design
- **Single Responsibility**: Each agent has one clear purpose
- **Stateless Design**: Agents don't maintain internal state
- **Error Handling**: Comprehensive exception handling
- **Logging**: Detailed execution logging

### 2. Workflow Design
- **Clear Dependencies**: Explicit execution order
- **Parallel Opportunities**: Maximize concurrent execution
- **Synchronization Points**: Minimize blocking operations
- **Fallback Strategies**: Graceful degradation paths

### 3. State Management
- **Immutable Updates**: Don't modify existing state entries
- **Type Safety**: Use TypedDict for state definitions
- **Validation**: Validate state at key points
- **Cleanup**: Remove unnecessary data to manage memory

## Next Steps

- [Financial Analysis Agents](./financial-analysis-agents.md) - Detailed agent implementations
- [StateGraph Workflows](./stategraph-workflows.md) - LangGraph implementation details
- [System Architecture](../technical/architecture.md) - Overall system design
