"""
<PERSON><PERSON><PERSON><PERSON> dashboard agent for generating KPI-based visualizations.
Creates dynamic dashboards based on user KPI queries using Plotly.
"""

from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langchain_groq import ChatGroq
from typing import List, Dict, Any, Optional
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@tool
def create_supplier_chart(data: str, kpi: str, chart_type: str = "bar") -> str:
    """
    Create a chart showing suppliers vs the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze (e.g., 'income', 'profit', 'cost')
        chart_type: Type of chart ('bar', 'pie', 'scatter')
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(data)
        
        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            # Try mapped columns
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Find supplier column
        supplier_col = None
        for col in df.columns:
            if 'supplier' in col.lower() or 'vendor' in col.lower():
                supplier_col = col
                break
        
        if not supplier_col:
            return json.dumps({"error": "No supplier column found"})
        
        # Aggregate data by supplier
        supplier_data = df.groupby(supplier_col)[target_column].sum().reset_index()
        supplier_data = supplier_data.sort_values(target_column, ascending=False).head(10)
        
        # Create chart based on type
        if chart_type == "bar":
            fig = px.bar(
                supplier_data, 
                x=supplier_col, 
                y=target_column,
                title=f"Top 10 Suppliers by {kpi.title()}",
                labels={target_column: kpi.title(), supplier_col: "Supplier"}
            )
        elif chart_type == "pie":
            fig = px.pie(
                supplier_data, 
                values=target_column, 
                names=supplier_col,
                title=f"Supplier Distribution by {kpi.title()}"
            )
        else:  # scatter
            fig = px.scatter(
                supplier_data, 
                x=supplier_col, 
                y=target_column,
                size=target_column,
                title=f"Suppliers vs {kpi.title()}",
                labels={target_column: kpi.title(), supplier_col: "Supplier"}
            )
        
        fig.update_layout(height=400)
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating supplier chart: {str(e)}"})


@tool
def create_product_chart(data: str, kpi: str, chart_type: str = "bar") -> str:
    """
    Create a chart showing products vs the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
        chart_type: Type of chart ('bar', 'pie', 'treemap')
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(data)
        
        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Find product column
        product_col = None
        for col in df.columns:
            if any(term in col.lower() for term in ['product', 'item', 'description', 'concept']):
                product_col = col
                break
        
        if not product_col:
            return json.dumps({"error": "No product column found"})
        
        # Aggregate data by product
        product_data = df.groupby(product_col)[target_column].sum().reset_index()
        product_data = product_data.sort_values(target_column, ascending=False).head(15)
        
        # Create chart based on type
        if chart_type == "bar":
            fig = px.bar(
                product_data, 
                x=product_col, 
                y=target_column,
                title=f"Top 15 Products by {kpi.title()}",
                labels={target_column: kpi.title(), product_col: "Product"}
            )
            fig.update_xaxes(tickangle=45)
        elif chart_type == "pie":
            fig = px.pie(
                product_data.head(10), 
                values=target_column, 
                names=product_col,
                title=f"Top 10 Products by {kpi.title()}"
            )
        else:  # treemap
            fig = px.treemap(
                product_data.head(10),
                values=target_column,
                names=product_col,
                title=f"Product Treemap by {kpi.title()}"
            )
        
        fig.update_layout(height=500)
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating product chart: {str(e)}"})


@tool
def create_total_summary_chart(data: str, kpi: str) -> str:
    """
    Create a summary chart showing total KPI with key metrics.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(data)
        
        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Calculate summary statistics
        total_value = df[target_column].sum()
        avg_value = df[target_column].mean()
        median_value = df[target_column].median()
        max_value = df[target_column].max()
        min_value = df[target_column].min()
        
        # Create gauge chart for total
        fig = go.Figure()
        
        # Add gauge
        fig.add_trace(go.Indicator(
            mode = "gauge+number+delta",
            value = total_value,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': f"Total {kpi.title()}"},
            gauge = {
                'axis': {'range': [None, max_value * 1.2]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, total_value * 0.5], 'color': "lightgray"},
                    {'range': [total_value * 0.5, total_value * 0.8], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': avg_value
                }
            }
        ))
        
        # Add annotations with key metrics
        fig.add_annotation(
            x=0.5, y=0.1,
            text=f"Avg: {avg_value:,.2f}<br>Median: {median_value:,.2f}<br>Max: {max_value:,.2f}",
            showarrow=False,
            font=dict(size=12)
        )
        
        fig.update_layout(
            title=f"{kpi.title()} Summary Dashboard",
            height=400
        )
        
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating summary chart: {str(e)}"})


@tool
def create_trend_chart(data: str, kpi: str) -> str:
    """
    Create a time-based trend chart for the specified KPI.
    
    Args:
        data: JSON string of the dataframe data
        kpi: The KPI to analyze
    
    Returns:
        JSON string of the Plotly figure
    """
    try:
        df = pd.read_json(data)
        
        # Find date column
        date_col = None
        for col in df.columns:
            if any(term in col.lower() for term in ['date', 'time', 'month', 'year']):
                date_col = col
                break
        
        if not date_col:
            return json.dumps({"error": "No date column found for trend analysis"})
        
        # Map KPI to appropriate columns
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # Find the appropriate column for the KPI
        target_column = None
        for col in df.columns:
            if kpi.lower() in col.lower():
                target_column = col
                break
        
        if not target_column:
            for mapped_col in kpi_mapping.get(kpi.lower(), []):
                if mapped_col in df.columns:
                    target_column = mapped_col
                    break
        
        if not target_column:
            return json.dumps({"error": f"No suitable column found for KPI: {kpi}"})
        
        # Convert date column to datetime
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        
        # Group by month and sum
        df['month_year'] = df[date_col].dt.to_period('M')
        monthly_data = df.groupby('month_year')[target_column].sum().reset_index()
        monthly_data['month_year'] = monthly_data['month_year'].astype(str)
        
        # Create line chart
        fig = px.line(
            monthly_data,
            x='month_year',
            y=target_column,
            title=f"{kpi.title()} Trend Over Time",
            labels={target_column: kpi.title(), 'month_year': 'Month'}
        )
        
        fig.update_traces(mode='lines+markers')
        fig.update_layout(height=400)
        fig.update_xaxes(tickangle=45)
        
        return fig.to_json()
        
    except Exception as e:
        return json.dumps({"error": f"Error creating trend chart: {str(e)}"})


class DashboardAgent:
    """LangChain agent for generating KPI-based dashboards."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama-3.1-8b-instant")
        
        # Define the tools available to the agent
        self.tools = [
            create_supplier_chart,
            create_product_chart,
            create_total_summary_chart,
            create_trend_chart
        ]
        
        # Create the prompt template
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a dashboard generation expert. Your job is to create comprehensive dashboards based on KPI queries.

When a user asks for a KPI dashboard (like 'income', 'profit', 'cost', 'volume'), you should:

1. Create a supplier-based chart showing the KPI by supplier
2. Create a product-based chart showing the KPI by product  
3. Create a total summary chart with key metrics
4. Create a trend chart if date data is available

Use the available tools to generate these visualizations. Always try to create multiple complementary charts that give a complete view of the KPI.

For each chart, choose the most appropriate chart type:
- Supplier charts: bar, pie, or scatter
- Product charts: bar, pie, or treemap  
- Summary charts: gauge with key metrics
- Trend charts: line charts over time

Return the results as a JSON object with the chart data."""),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}")
        ])
        
        # Create the agent
        self.agent = create_openai_tools_agent(self.llm, self.tools, self.prompt)
        self.agent_executor = AgentExecutor(agent=self.agent, tools=self.tools, verbose=True)
    
    def generate_dashboard(self, data: pd.DataFrame, kpi_query: str) -> Dict[str, Any]:
        """
        Generate a dashboard based on the KPI query.

        Args:
            data: The dataframe to analyze
            kpi_query: The KPI query (e.g., "income", "show me profit dashboard")

        Returns:
            Dictionary containing the generated charts
        """
        try:
            # Extract KPI from query
            kpi = self._extract_kpi(kpi_query)

            # Limit data size to avoid token limits - take sample if too large
            if len(data) > 100:
                sample_data = data.sample(n=100, random_state=42)
                data_summary = f"Sample of {len(sample_data)} records from {len(data)} total records"
            else:
                sample_data = data
                data_summary = f"All {len(data)} records"

            # Convert sample dataframe to JSON for the tools
            data_json = sample_data.to_json()

            # Create a simplified input for the agent
            input_text = f"""
            Generate dashboard for KPI: {kpi}

            Data info: {data_summary}
            Columns: {list(data.columns)}

            Create charts using the tools available. Use data: {data_json}
            """

            # Execute the agent
            result = self.agent_executor.invoke({"input": input_text})

            return {
                "success": True,
                "kpi": kpi,
                "dashboard_data": result.get("output", ""),
                "charts_generated": 4,
                "data_summary": data_summary
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error generating dashboard: {str(e)}",
                "kpi": kpi_query
            }
    
    def _extract_kpi(self, query: str) -> str:
        """Extract the main KPI from the user query."""
        query_lower = query.lower()
        
        # Common KPI keywords
        kpi_keywords = {
            'income': ['income', 'revenue', 'sales', 'earnings'],
            'profit': ['profit', 'margin', 'profitability'],
            'cost': ['cost', 'expense', 'spending'],
            'volume': ['volume', 'quantity', 'units', 'count']
        }
        
        for kpi, keywords in kpi_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return kpi
        
        # Default to income if no specific KPI found
        return 'income'


# Initialize the dashboard agent
dashboard_agent = DashboardAgent()


def generate_kpi_dashboard(data: pd.DataFrame, kpi_query: str) -> Dict[str, Any]:
    """
    Main function to generate KPI dashboard.
    
    Args:
        data: The dataframe to analyze
        kpi_query: The KPI query from the user
    
    Returns:
        Dictionary containing dashboard results
    """
    return dashboard_agent.generate_dashboard(data, kpi_query)
