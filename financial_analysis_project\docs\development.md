# 🛠️ Development Guide

Guide for developers who want to contribute to or extend the Financial Analysis Project.

## 🏗️ Development Environment Setup

### Prerequisites
- Python 3.8+
- Git
- Code editor (VS Code recommended)
- GROQ API key

### Initial Setup
```bash
# Clone repository
git clone <repository-url>
cd financial_analysis_project

# Create virtual environment
python -m venv dev_env
source dev_env/bin/activate  # Linux/Mac
dev_env\Scripts\activate     # Windows

# Install dependencies
pip install -r backend/requirements.txt
pip install -r frontend/requirements.txt

# Install development dependencies
pip install pytest black flake8 mypy pre-commit
```

### Environment Configuration
```bash
# Copy environment template
cp backend/.env.example backend/.env

# Add your API key
echo "GROQ_API_KEY=your_key_here" >> backend/.env
```

## 📁 Project Structure

```
financial_analysis_project/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── main.py            # FastAPI application
│   │   ├── agents/            # LangChain agents
│   │   │   └── pandas_agents.py
│   │   ├── services/          # Business logic
│   │   │   ├── excel_processor.py
│   │   │   └── financial_analyzer.py
│   │   ├── models/            # Data models
│   │   └── utils/             # Utilities
│   └── requirements.txt
├── frontend/                   # Streamlit frontend
│   ├── streamlit_app.py
│   └── components/
├── shared/                     # Shared schemas
│   └── schemas.py
├── tests/                      # Test suite
├── docs/                       # Documentation
└── sample_data/               # Sample files
```

## 🔧 Development Workflow

### 1. Code Style and Formatting

#### Black (Code Formatter)
```bash
# Format all Python files
black .

# Check formatting
black --check .
```

#### Flake8 (Linter)
```bash
# Check code style
flake8 backend/ frontend/ tests/

# Configuration in setup.cfg
[flake8]
max-line-length = 88
exclude = .git,__pycache__,venv
```

#### MyPy (Type Checking)
```bash
# Type checking
mypy backend/app/

# Configuration in mypy.ini
[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
```

### 2. Pre-commit Hooks
```bash
# Install pre-commit hooks
pre-commit install

# Run manually
pre-commit run --all-files
```

### 3. Testing

#### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=backend/app tests/

# Run specific test file
pytest tests/test_backend.py -v

# Run specific test
pytest tests/test_backend.py::TestExcelProcessor::test_column_mapping -v
```

#### Test Structure
```python
# tests/test_example.py
import pytest
from backend.app.services.excel_processor import ExcelProcessor

class TestExcelProcessor:
    def setup_method(self):
        """Setup before each test method."""
        self.processor = ExcelProcessor()
    
    def test_column_mapping(self):
        """Test column mapping functionality."""
        # Test implementation
        assert True
```

### 4. Adding New Features

#### Backend Development

##### Adding New API Endpoints
```python
# backend/app/main.py
@app.post("/new-endpoint")
async def new_endpoint(request: NewRequest):
    """New endpoint description."""
    try:
        # Implementation
        return {"success": True, "data": result}
    except Exception as e:
        return {"success": False, "error": str(e)}
```

##### Adding New Services
```python
# backend/app/services/new_service.py
class NewService:
    """New service description."""
    
    def __init__(self):
        self.config = load_config()
    
    def process(self, data):
        """Process data."""
        # Implementation
        return result
```

##### Adding New Agents
```python
# backend/app/agents/pandas_agents.py
class NewAnalysisAgent:
    """New analysis agent."""
    
    def __init__(self):
        self.llm = ChatGroq(temperature=0, model="llama3-70b-8192")
    
    def execute(self, df: pd.DataFrame) -> str:
        """Execute analysis."""
        # Create pandas agent
        agent = create_pandas_dataframe_agent(
            llm=self.llm,
            df=df,
            verbose=True,
            allow_dangerous_code=True
        )
        
        # Execute query
        response = agent.invoke({"input": "Analysis query"})
        return response["output"]
```

#### Frontend Development

##### Adding New Components
```python
# frontend/components/new_component.py
import streamlit as st

class NewComponent:
    """New UI component."""
    
    def render(self, data):
        """Render component."""
        st.subheader("New Component")
        # Implementation
```

##### Adding New Visualizations
```python
# Using Plotly
import plotly.express as px

def create_new_chart(data):
    """Create new chart."""
    fig = px.bar(data, x='category', y='value')
    return fig

# In Streamlit app
fig = create_new_chart(analysis_data)
st.plotly_chart(fig, use_container_width=True)
```

### 5. Database Integration (Future)

#### Adding Database Models
```python
# backend/app/models/database.py
from sqlalchemy import Column, Integer, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class AnalysisResult(Base):
    __tablename__ = "analysis_results"
    
    id = Column(Integer, primary_key=True)
    file_name = Column(String)
    total_amount = Column(Float)
    created_at = Column(DateTime)
```

## 🧪 Testing Guidelines

### 1. Test Categories

#### Unit Tests
- Test individual functions/methods
- Mock external dependencies
- Fast execution

#### Integration Tests
- Test component interactions
- Use test databases
- Slower execution

#### End-to-End Tests
- Test complete workflows
- Use real services
- Slowest execution

### 2. Test Data Management

#### Creating Test Data
```python
# tests/fixtures.py
import pandas as pd
import pytest

@pytest.fixture
def sample_financial_data():
    """Create sample financial data for testing."""
    return pd.DataFrame({
        'supplier': ['ABC Corp', 'XYZ Ltd'],
        'voucher': ['INV-001', 'INV-002'],
        'gross_amount': [10000, 15000],
        'cost': [7000, 10500]
    })
```

#### Using Test Files
```python
# tests/test_with_files.py
import os
from pathlib import Path

def test_excel_processing():
    """Test with actual Excel file."""
    test_file = Path(__file__).parent / "data" / "test_file.xlsx"
    # Test implementation
```

### 3. Mocking External Services

#### Mocking LLM Calls
```python
from unittest.mock import Mock, patch

@patch('backend.app.agents.pandas_agents.ChatGroq')
def test_agent_execution(mock_llm):
    """Test agent with mocked LLM."""
    mock_llm.return_value.invoke.return_value = "Mocked response"
    
    # Test implementation
    agent = GrossAmountAnalyzer()
    result = agent.execute(sample_data)
    
    assert "Mocked response" in result
```

## 📊 Performance Optimization

### 1. Backend Optimization

#### Async Processing
```python
import asyncio
from fastapi import BackgroundTasks

@app.post("/analyze-async")
async def analyze_async(background_tasks: BackgroundTasks, file: UploadFile):
    """Async analysis endpoint."""
    background_tasks.add_task(process_file_async, file)
    return {"message": "Analysis started"}

async def process_file_async(file):
    """Process file asynchronously."""
    # Implementation
```

#### Caching
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def expensive_calculation(data_hash):
    """Cache expensive calculations."""
    # Implementation
    return result
```

#### Database Optimization
```python
# Use connection pooling
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20
)
```

### 2. Frontend Optimization

#### Streamlit Caching
```python
@st.cache_data
def load_data(file_path):
    """Cache data loading."""
    return pd.read_excel(file_path)

@st.cache_resource
def create_analyzer():
    """Cache resource creation."""
    return FinancialAnalyzer()
```

## 🚀 Deployment

### 1. Docker Setup

#### Backend Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
  
  frontend:
    build: ./frontend
    ports:
      - "8501:8501"
    depends_on:
      - backend
```

### 2. Environment Management

#### Production Settings
```python
# backend/app/config.py
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    groq_api_key: str
    debug: bool = False
    max_file_size: int = 50 * 1024 * 1024
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## 📝 Documentation

### 1. Code Documentation

#### Docstring Standards
```python
def analyze_financial_data(df: pd.DataFrame, config: dict) -> dict:
    """
    Analyze financial data using AI agents.
    
    Args:
        df: DataFrame containing financial transactions
        config: Configuration parameters for analysis
        
    Returns:
        Dictionary containing analysis results
        
    Raises:
        ValueError: If required columns are missing
        AnalysisError: If analysis fails
        
    Example:
        >>> config = {"assume_cost_percentage": 70.0}
        >>> result = analyze_financial_data(df, config)
        >>> print(result["total_amount"])
    """
    # Implementation
```

### 2. API Documentation

#### OpenAPI/Swagger
FastAPI automatically generates API documentation at `/docs`

#### Custom Documentation
```python
@app.post("/analyze", 
    summary="Analyze Financial Data",
    description="Upload Excel file and perform comprehensive financial analysis",
    response_description="Analysis results with supplier summaries and trends"
)
async def analyze(file: UploadFile = File(...)):
    """Detailed endpoint documentation."""
    pass
```

## 🔍 Debugging

### 1. Backend Debugging

#### Logging Setup
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
```

#### Debug Mode
```python
# Enable debug mode
if settings.debug:
    import debugpy
    debugpy.listen(5678)
    debugpy.wait_for_client()
```

### 2. Frontend Debugging

#### Streamlit Debugging
```python
# Debug information
st.write("Debug info:", st.session_state)

# Error handling
try:
    result = api_call()
except Exception as e:
    st.error(f"Error: {e}")
    st.exception(e)  # Show full traceback
```

---

**Next Steps**: Review the [Contributing Guidelines](contributing.md) for information about submitting changes.
