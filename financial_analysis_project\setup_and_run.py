"""
Setup and run script for the Financial Analysis Project.
This script helps set up the environment and run the application.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path


def print_banner():
    """Print project banner."""
    print("=" * 60)
    print("🏦 FINANCIAL ANALYSIS PROJECT SETUP")
    print("=" * 60)
    print("AI-powered financial analysis using LangChain pandas agents")
    print("FastAPI backend + Streamlit frontend")
    print("=" * 60)


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def install_dependencies():
    """Install project dependencies."""
    print("\n📦 Installing dependencies...")

    # Backend dependencies
    print("Installing backend dependencies...")
    backend_path = Path("backend")
    if backend_path.exists():
        try:
            # First, upgrade pip and install wheel
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip", "wheel"
            ], check=True)

            # Install numpy and pandas first with precompiled binaries
            print("Installing NumPy and Pandas with precompiled binaries...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--only-binary=all",
                "numpy>=1.26.0", "pandas>=2.2.0"
            ], check=True)

            # Then install other dependencies
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r",
                str(backend_path / "requirements.txt")
            ], check=True, cwd=backend_path.parent)
            print("✅ Backend dependencies installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install backend dependencies: {e}")
            print("💡 Try using Python 3.11 instead of 3.12 for better compatibility")
            return False
    
    # Frontend dependencies
    print("Installing frontend dependencies...")
    frontend_path = Path("frontend")
    if frontend_path.exists():
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", 
                str(frontend_path / "requirements.txt")
            ], check=True, cwd=frontend_path.parent)
            print("✅ Frontend dependencies installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install frontend dependencies: {e}")
            return False
    
    return True


def setup_environment():
    """Set up environment variables."""
    print("\n🔧 Setting up environment...")
    
    env_example_path = Path("backend/.env.example")
    env_path = Path("backend/.env")
    
    if env_example_path.exists() and not env_path.exists():
        print("Creating .env file from template...")
        
        # Copy example to .env
        with open(env_example_path, 'r') as f:
            content = f.read()
        
        with open(env_path, 'w') as f:
            f.write(content)
        
        print("✅ .env file created")
        print("⚠️  Please edit backend/.env and add your GROQ_API_KEY")
        print("   You can get a free API key from: https://console.groq.com/")
        
        return False  # Need user to add API key
    elif env_path.exists():
        print("✅ .env file already exists")
        
        # Check if API key is set
        with open(env_path, 'r') as f:
            content = f.read()
        
        if "your_groq_api_key_here" in content:
            print("⚠️  Please update your GROQ_API_KEY in backend/.env")
            return False
        
        print("✅ Environment configuration looks good")
        return True
    else:
        print("❌ .env.example file not found")
        return False


def create_sample_data():
    """Create sample data files."""
    print("\n📊 Creating sample data...")
    
    sample_script = Path("sample_data/create_sample_data.py")
    if sample_script.exists():
        try:
            subprocess.run([sys.executable, str(sample_script)], check=True)
            print("✅ Sample data created successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create sample data: {e}")
            return False
    else:
        print("⚠️  Sample data script not found")
        return False


def run_tests():
    """Run the test suite."""
    print("\n🧪 Running tests...")
    
    try:
        # Install pytest if not available
        subprocess.run([sys.executable, "-m", "pip", "install", "pytest"], 
                      check=True, capture_output=True)
        
        # Run tests
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("⚠️  Some tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to run tests: {e}")
        return False


def start_backend():
    """Start the FastAPI backend."""
    print("\n🚀 Starting FastAPI backend...")
    
    backend_path = Path("backend")
    if not backend_path.exists():
        print("❌ Backend directory not found")
        return None
    
    try:
        # Start backend in background
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "app.main:app", 
            "--reload", "--port", "8000"
        ], cwd=backend_path)
        
        # Wait for backend to start
        print("Waiting for backend to start...")
        for i in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get("http://localhost:8000/health", timeout=1)
                if response.status_code == 200:
                    print("✅ Backend started successfully!")
                    print("🔗 Backend API: http://localhost:8000")
                    print("📚 API Docs: http://localhost:8000/docs")
                    return process
            except:
                pass
            
            time.sleep(1)
            print(f"⏳ Waiting... ({i+1}/30)")
        
        print("❌ Backend failed to start within 30 seconds")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None


def start_frontend():
    """Start the Streamlit frontend."""
    print("\n🎨 Starting Streamlit frontend...")
    
    frontend_path = Path("frontend")
    if not frontend_path.exists():
        print("❌ Frontend directory not found")
        return None
    
    try:
        # Start frontend
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501"
        ], cwd=frontend_path)
        
        print("✅ Frontend starting...")
        print("🔗 Frontend URL: http://localhost:8501")
        
        return process
        
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None


def main():
    """Main setup and run function."""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        return
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed. Please check the errors above.")
        return
    
    # Setup environment
    env_ready = setup_environment()
    
    # Create sample data
    create_sample_data()
    
    # Run tests
    print("\nWould you like to run the test suite? (y/n): ", end="")
    if input().lower().startswith('y'):
        run_tests()
    
    if not env_ready:
        print("\n⚠️  Please configure your environment variables before running the application.")
        print("Edit backend/.env and add your GROQ_API_KEY, then run this script again.")
        return
    
    print("\n🚀 Starting the application...")
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        return
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        return
    
    print("\n" + "=" * 60)
    print("🎉 APPLICATION STARTED SUCCESSFULLY!")
    print("=" * 60)
    print("🔗 Frontend (Streamlit): http://localhost:8501")
    print("🔗 Backend API: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("=" * 60)
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:8501 in your browser")
    print("2. Upload a sample Excel file from the sample_data/ directory")
    print("3. Configure analysis parameters and click 'Analyze'")
    print("4. View comprehensive financial analysis results")
    print("\n💡 Sample files available:")
    print("- sample_data/sample_financial_data.xlsx (main dataset)")
    print("- sample_data/problematic_financial_data.xlsx (edge cases)")
    print("- sample_data/datos_financieros_es.xlsx (Spanish columns)")
    
    print("\n⏹️  Press Ctrl+C to stop both services")
    
    try:
        # Wait for user to stop
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping services...")
        
        if frontend_process:
            frontend_process.terminate()
            print("✅ Frontend stopped")
        
        if backend_process:
            backend_process.terminate()
            print("✅ Backend stopped")
        
        print("👋 Goodbye!")


if __name__ == "__main__":
    main()
