"""
Results display component for Streamlit frontend.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Any
import json
from datetime import datetime


class ResultsDisplayComponent:
    """Component for displaying financial analysis results."""
    
    def __init__(self):
        self.color_palette = {
            'primary': '#1f77b4',
            'success': '#2ca02c',
            'warning': '#ff7f0e',
            'danger': '#d62728',
            'info': '#17becf'
        }
    
    def render_summary_metrics(self, results: Dict[str, Any]):
        """Render key summary metrics."""
        st.subheader("📊 Analysis Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Total Records",
                f"{results['total_records_processed']:,}",
                help="Total number of transactions processed"
            )
        
        with col2:
            st.metric(
                "Unique Suppliers",
                f"{results['total_suppliers']:,}",
                help="Number of unique suppliers in the dataset"
            )
        
        with col3:
            st.metric(
                "Total Gross Amount",
                f"${results['total_gross_amount']:,.2f}",
                help="Sum of all gross transaction amounts"
            )
        
        with col4:
            processing_time = results.get('processing_time_seconds', 0)
            st.metric(
                "Processing Time",
                f"{processing_time:.1f}s",
                help="Time taken to complete the analysis"
            )
    
    def render_supplier_analysis(self, supplier_summaries: List[Dict]):
        """Render supplier analysis charts and tables."""
        st.subheader("🏢 Supplier Analysis")
        
        if not supplier_summaries:
            st.info("No supplier data available.")
            return
        
        # Convert to DataFrame
        df = pd.DataFrame(supplier_summaries)
        
        # Top suppliers chart
        top_10 = df.head(10)
        
        fig = px.bar(
            top_10,
            x='supplier_name',
            y='total_gross_amount',
            color='average_margin',
            color_continuous_scale='RdYlGn',
            title="Top 10 Suppliers by Gross Amount",
            labels={
                'supplier_name': 'Supplier',
                'total_gross_amount': 'Total Gross Amount ($)',
                'average_margin': 'Average Margin (%)'
            }
        )
        fig.update_xaxes(tickangle=45)
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)
        
        # Supplier performance table
        st.subheader("📋 Supplier Performance Table")
        
        display_df = df.copy()
        display_df['total_gross_amount'] = display_df['total_gross_amount'].apply(lambda x: f"${x:,.2f}")
        display_df['average_margin'] = display_df['average_margin'].apply(
            lambda x: f"{x:.1f}%" if pd.notna(x) else "N/A"
        )
        
        # Rename columns for display
        display_df = display_df.rename(columns={
            'supplier_name': 'Supplier',
            'total_gross_amount': 'Total Gross Amount',
            'total_transactions': 'Transactions',
            'average_margin': 'Avg Margin %',
            'low_margin_transactions': 'Low Margin Count',
            'negative_margin_transactions': 'Negative Margin Count'
        })
        
        st.dataframe(display_df, use_container_width=True)
    
    def render_risk_analysis(self, low_margin_transactions: List[Dict], 
                           negative_margin_transactions: List[Dict]):
        """Render risk analysis for low and negative margin transactions."""
        st.subheader("⚠️ Risk Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric(
                "Low Margin Transactions",
                len(low_margin_transactions),
                help="Transactions with margins below the threshold"
            )
        
        with col2:
            st.metric(
                "Negative Margin Transactions",
                len(negative_margin_transactions),
                help="Transactions with negative profit margins"
            )
        
        # Risk distribution chart
        if low_margin_transactions or negative_margin_transactions:
            risk_data = {
                'Risk Level': ['Low Margin', 'Negative Margin', 'Normal'],
                'Count': [
                    len(low_margin_transactions),
                    len(negative_margin_transactions),
                    0  # This would need to be calculated from total transactions
                ]
            }
            
            fig = px.pie(
                values=risk_data['Count'][:2],  # Only show low and negative
                names=risk_data['Risk Level'][:2],
                title="Risk Distribution",
                color_discrete_sequence=[self.color_palette['warning'], self.color_palette['danger']]
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Display problematic transactions
        if low_margin_transactions:
            st.subheader("⚠️ Low Margin Transactions (Top 20)")
            self._display_transaction_table(low_margin_transactions[:20], "low_margin")
        
        if negative_margin_transactions:
            st.subheader("🚨 Negative Margin Transactions (Top 20)")
            self._display_transaction_table(negative_margin_transactions[:20], "negative_margin")
    
    def _display_transaction_table(self, transactions: List[Dict], table_type: str):
        """Display a table of transactions."""
        if not transactions:
            st.info("No transactions to display.")
            return
        
        df = pd.DataFrame(transactions)
        
        # Format for display
        display_df = df.copy()
        display_df['gross_amount'] = display_df['gross_amount'].apply(lambda x: f"${x:,.2f}")
        
        if 'margin_percentage' in display_df.columns:
            display_df['margin_percentage'] = display_df['margin_percentage'].apply(
                lambda x: f"{x:.1f}%" if pd.notna(x) else "N/A"
            )
        
        # Rename columns
        column_mapping = {
            'voucher_id': 'Voucher ID',
            'supplier_name': 'Supplier',
            'gross_amount': 'Gross Amount',
            'margin_percentage': 'Margin %',
            'transaction_date': 'Date'
        }
        
        display_df = display_df.rename(columns=column_mapping)
        
        # Select relevant columns
        columns_to_show = ['Voucher ID', 'Supplier', 'Gross Amount', 'Margin %']
        if 'Date' in display_df.columns:
            columns_to_show.append('Date')
        
        display_df = display_df[columns_to_show]
        
        st.dataframe(display_df, use_container_width=True)
    
    def render_trend_analysis(self, monthly_trends: List[Dict]):
        """Render month-over-month trend analysis."""
        st.subheader("📈 Monthly Trends")
        
        if not monthly_trends:
            st.info("No trend data available.")
            return
        
        df = pd.DataFrame(monthly_trends)
        
        # Overall monthly trend
        monthly_totals = df.groupby('year_month')['total_gross_amount'].sum().reset_index()
        
        fig1 = px.line(
            monthly_totals,
            x='year_month',
            y='total_gross_amount',
            title="Monthly Gross Amount Trend",
            labels={
                'year_month': 'Month',
                'total_gross_amount': 'Total Gross Amount ($)'
            }
        )
        fig1.update_layout(height=400)
        st.plotly_chart(fig1, use_container_width=True)
        
        # Top suppliers trend
        top_suppliers = df.groupby('supplier_name')['total_gross_amount'].sum().nlargest(5).index
        supplier_trends = df[df['supplier_name'].isin(top_suppliers)]
        
        if not supplier_trends.empty:
            fig2 = px.line(
                supplier_trends,
                x='year_month',
                y='total_gross_amount',
                color='supplier_name',
                title="Top 5 Suppliers - Monthly Trends",
                labels={
                    'year_month': 'Month',
                    'total_gross_amount': 'Gross Amount ($)',
                    'supplier_name': 'Supplier'
                }
            )
            fig2.update_layout(height=400)
            st.plotly_chart(fig2, use_container_width=True)
        
        # Margin trends
        margin_data = df[df['profit_margin'].notna()]
        if not margin_data.empty:
            monthly_margins = margin_data.groupby('year_month')['profit_margin'].mean().reset_index()
            
            fig3 = px.line(
                monthly_margins,
                x='year_month',
                y='profit_margin',
                title="Average Monthly Profit Margin Trend",
                labels={
                    'year_month': 'Month',
                    'profit_margin': 'Average Profit Margin (%)'
                }
            )
            fig3.update_layout(height=400)
            st.plotly_chart(fig3, use_container_width=True)
    
    def render_detailed_info(self, results: Dict[str, Any]):
        """Render detailed analysis information."""
        st.subheader("📋 Analysis Details")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📝 Assumptions Made")
            assumptions = results.get('assumptions_used', [])
            if assumptions:
                for assumption in assumptions:
                    st.info(f"ℹ️ {assumption}")
            else:
                st.info("No specific assumptions were made.")
        
        with col2:
            st.subheader("⚠️ Warnings")
            warnings = results.get('warnings', [])
            if warnings:
                for warning in warnings:
                    st.warning(f"⚠️ {warning}")
            else:
                st.success("✅ No warnings generated.")
    
    def render_download_options(self, results: Dict[str, Any]):
        """Render download options for analysis results."""
        st.subheader("💾 Download Results")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # JSON download
            results_json = json.dumps(results, indent=2, default=str)
            st.download_button(
                label="📥 Download JSON",
                data=results_json,
                file_name=f"financial_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json",
                help="Download complete analysis results as JSON"
            )
        
        with col2:
            # CSV download for supplier summaries
            if results.get('supplier_summaries'):
                supplier_df = pd.DataFrame(results['supplier_summaries'])
                csv_data = supplier_df.to_csv(index=False)
                st.download_button(
                    label="📊 Download Supplier CSV",
                    data=csv_data,
                    file_name=f"supplier_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    help="Download supplier analysis as CSV"
                )
        
        with col3:
            # CSV download for risk transactions
            risk_transactions = results.get('low_margin_transactions', []) + results.get('negative_margin_transactions', [])
            if risk_transactions:
                risk_df = pd.DataFrame(risk_transactions)
                csv_data = risk_df.to_csv(index=False)
                st.download_button(
                    label="⚠️ Download Risk CSV",
                    data=csv_data,
                    file_name=f"risk_transactions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    help="Download risk transactions as CSV"
                )
