# Financial Analysis Tutorial

This comprehensive tutorial will guide you through using the Financial Analysis system to analyze Excel financial data using AI-powered agents.

## Overview

The Financial Analysis system processes Excel files containing financial transaction data and provides:
- Supplier and voucher analysis
- Profit margin calculations
- Trend analysis
- Comprehensive financial reports

## Prerequisites

- System installed and running (see [Quick Start Guide](../quick-start.md))
- Excel file with financial data
- Basic understanding of financial concepts

## Tutorial Structure

1. **Data Preparation** - Preparing your Excel file
2. **Using the Web Interface** - Streamlit frontend
3. **Using the API** - Direct API calls
4. **Understanding Results** - Interpreting the analysis
5. **Advanced Features** - Customization options

## 1. Data Preparation

### Required Excel Structure

Your Excel file should contain the following types of columns:

#### Essential Columns
- **Supplier/Vendor**: Company or entity providing goods/services
- **Voucher/Transaction ID**: Unique identifier for each transaction
- **Amount/Value**: Transaction amounts (gross, net, cost)
- **Date**: Transaction dates

#### Optional Columns
- **Cost**: Cost of goods/services (if not provided, system will estimate)
- **Category**: Transaction categories
- **Currency**: If multi-currency transactions
- **Description**: Transaction descriptions

### Sample Data Structure
```
| Supplier    | Voucher | Date       | Gross_Amount | Cost   | Category |
|-------------|---------|------------|--------------|--------|----------|
| Supplier A  | V001    | 2024-01-15 | 1000.00     | 800.00 | Services |
| Supplier B  | V002    | 2024-01-16 | 1500.00     | 1200.00| Products |
| Supplier A  | V003    | 2024-02-01 | 2000.00     | 1500.00| Services |
```

### Data Quality Tips

1. **Consistent Naming**: Use consistent supplier names
2. **Date Formats**: Use standard date formats (YYYY-MM-DD preferred)
3. **Numeric Values**: Ensure amounts are numeric (no currency symbols in data)
4. **No Empty Rows**: Remove empty rows between data
5. **Header Row**: Include clear column headers

### Using Sample Data

The system includes sample files you can use for testing:

```bash
# Navigate to sample data
cd financial_analysis_project/sample_data/

# Available files:
ls -la
# sample_financial_data.xlsx          - Basic sample
# comprehensive_financial_data.xlsx   - Complex sample with multiple scenarios
# datos_financieros_es.xlsx          - Spanish language sample
# problematic_financial_data.xlsx    - Sample with data quality issues
```

## 2. Using the Web Interface

### Starting the Interface

```bash
# Start the backend
cd financial_analysis_project/backend
uvicorn app.main:app --reload --port 8000

# In another terminal, start the frontend
cd financial_analysis_project/frontend
streamlit run streamlit_app.py
```

### Step-by-Step Analysis

#### Step 1: Access the Interface
Open your browser and navigate to `http://localhost:8501`

#### Step 2: Upload Your File
1. Click the "Browse files" button
2. Select your Excel file
3. Wait for the upload confirmation

#### Step 3: Configure Analysis (Optional)
- **Cost Assumption**: If your data lacks cost information, set the cost percentage (default: 70%)
- **Analysis Depth**: Choose between quick or comprehensive analysis
- **Date Range**: Optionally filter by date range

#### Step 4: Run Analysis
Click "Analyze Financial Data" and wait for processing to complete.

#### Step 5: Review Results
The interface will display:
- **Executive Summary**: Key findings and insights
- **Supplier Analysis**: Top suppliers and performance metrics
- **Voucher Analysis**: Transaction-level insights
- **Margin Analysis**: Profitability assessment
- **Trend Analysis**: Time-based patterns
- **Downloadable Report**: PDF or Excel export

### Interface Features

#### Interactive Charts
- **Supplier Performance**: Bar charts showing top suppliers
- **Margin Distribution**: Histogram of profit margins
- **Trend Lines**: Time series of profitability
- **Risk Indicators**: Visual alerts for low-margin transactions

#### Filtering Options
- **Date Range**: Filter analysis by specific periods
- **Supplier Filter**: Focus on specific suppliers
- **Amount Thresholds**: Filter by transaction size
- **Margin Thresholds**: Focus on high/low margin transactions

## 3. Using the API

### Direct API Calls

#### Basic Analysis
```bash
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@your_financial_data.xlsx"
```

#### With Parameters
```bash
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@your_financial_data.xlsx" \
  -F "cost_assumption=0.75" \
  -F "analysis_type=comprehensive"
```

### Python Integration

```python
import requests

# Prepare file and parameters
files = {'file': open('your_financial_data.xlsx', 'rb')}
data = {
    'cost_assumption': 0.70,
    'analysis_type': 'comprehensive'
}

# Make API call
response = requests.post(
    'http://localhost:8000/analyze',
    files=files,
    data=data
)

# Process results
if response.status_code == 200:
    results = response.json()
    print("Analysis completed successfully!")
    print(f"Supplier Analysis: {results['supplier_analysis']}")
    print(f"Final Report: {results['final_report']}")
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### JavaScript Integration

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('cost_assumption', '0.70');

fetch('http://localhost:8000/analyze', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('Analysis results:', data);
    displayResults(data);
})
.catch(error => {
    console.error('Error:', error);
});
```

## 4. Understanding Results

### Analysis Components

#### Supplier Analysis
```json
{
  "supplier_analysis": {
    "top_suppliers": [
      {
        "name": "Supplier A",
        "total_amount": 15000.00,
        "transaction_count": 12,
        "average_transaction": 1250.00
      }
    ],
    "supplier_rankings": "...",
    "performance_metrics": "..."
  }
}
```

**Key Metrics**:
- **Total Amount**: Sum of all transactions
- **Transaction Count**: Number of transactions
- **Average Transaction**: Mean transaction value
- **Market Share**: Percentage of total spend

#### Voucher Analysis
```json
{
  "voucher_analysis": {
    "transaction_summary": "...",
    "large_transactions": "...",
    "frequency_analysis": "..."
  }
}
```

**Key Insights**:
- **Transaction Distribution**: Size and frequency patterns
- **Outlier Detection**: Unusually large or small transactions
- **Temporal Patterns**: Transaction timing analysis

#### Margin Analysis
```json
{
  "margin_analysis": {
    "overall_margin": 23.5,
    "low_margin_transactions": [
      {
        "voucher": "V123",
        "supplier": "Supplier X",
        "margin": 5.2,
        "risk_level": "high"
      }
    ],
    "margin_distribution": "..."
  }
}
```

**Risk Indicators**:
- **Red Flags**: Margins < 10% or negative
- **Yellow Flags**: Margins 10-15%
- **Green**: Margins > 15%

#### Trend Analysis
```json
{
  "trend_analysis": {
    "monthly_trends": "...",
    "supplier_trends": "...",
    "seasonal_patterns": "..."
  }
}
```

**Trend Insights**:
- **Growth Patterns**: Increasing/decreasing trends
- **Seasonality**: Recurring patterns
- **Supplier Evolution**: Performance changes over time

### Final Report Structure

The comprehensive report includes:

1. **Executive Summary**
   - Key findings
   - Overall financial health
   - Critical recommendations

2. **Detailed Analysis**
   - Supplier performance breakdown
   - Transaction analysis
   - Margin assessment
   - Trend insights

3. **Risk Assessment**
   - Low-margin transactions
   - Supplier concentration risks
   - Trend-based risks

4. **Recommendations**
   - Actionable insights
   - Optimization opportunities
   - Risk mitigation strategies

## 5. Advanced Features

### Custom Analysis Parameters

#### Cost Assumptions
When cost data is missing, the system can estimate costs:
```python
# Different cost assumption strategies
cost_strategies = {
    "conservative": 0.80,  # 80% of gross amount
    "moderate": 0.70,      # 70% of gross amount
    "aggressive": 0.60     # 60% of gross amount
}
```

#### Analysis Depth
```python
analysis_types = {
    "quick": "Basic metrics and top-level insights",
    "standard": "Comprehensive analysis with trends",
    "deep": "Advanced analytics with predictive insights"
}
```

### Custom Prompts

You can customize the analysis by modifying agent prompts:

```python
# Example: Custom margin analysis prompt
custom_margin_prompt = """
Analyze profit margins with focus on:
1. Industry-specific margin benchmarks
2. Seasonal margin variations
3. Supplier-specific margin patterns
4. Risk-adjusted margin calculations

Apply the following thresholds:
- High risk: < 5%
- Medium risk: 5-12%
- Low risk: > 12%
"""
```

### Batch Processing

Process multiple files:
```python
import os
import requests

def batch_analyze(directory_path):
    results = {}
    
    for filename in os.listdir(directory_path):
        if filename.endswith('.xlsx'):
            file_path = os.path.join(directory_path, filename)
            
            with open(file_path, 'rb') as file:
                files = {'file': file}
                response = requests.post(
                    'http://localhost:8000/analyze',
                    files=files
                )
                
                if response.status_code == 200:
                    results[filename] = response.json()
                else:
                    results[filename] = {"error": response.text}
    
    return results

# Usage
batch_results = batch_analyze('./financial_data/')
```

### Integration with Business Intelligence

#### Export to BI Tools
```python
def export_to_powerbi(analysis_results):
    """Export results in Power BI compatible format."""
    # Transform results for BI consumption
    bi_data = {
        "suppliers": extract_supplier_metrics(analysis_results),
        "transactions": extract_transaction_data(analysis_results),
        "trends": extract_trend_data(analysis_results)
    }
    
    # Save as CSV for Power BI import
    pd.DataFrame(bi_data["suppliers"]).to_csv("suppliers_analysis.csv")
    pd.DataFrame(bi_data["transactions"]).to_csv("transactions_analysis.csv")
    pd.DataFrame(bi_data["trends"]).to_csv("trends_analysis.csv")
```

## Troubleshooting

### Common Issues

#### "Column not found" Error
**Problem**: Excel file missing required columns
**Solution**: Ensure your file has supplier, voucher, amount, and date columns

#### "Invalid date format" Error
**Problem**: Date columns not recognized
**Solution**: Use standard date formats (YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY)

#### "Analysis timeout" Error
**Problem**: Large files taking too long to process
**Solution**: 
- Reduce file size
- Use "quick" analysis mode
- Increase timeout settings

#### "Low quality results" Warning
**Problem**: Data quality issues affecting analysis
**Solution**:
- Clean data before upload
- Remove empty rows and columns
- Standardize supplier names
- Validate numeric fields

### Performance Tips

1. **File Size**: Keep files under 10MB for optimal performance
2. **Data Quality**: Clean data produces better results
3. **Column Names**: Use clear, descriptive column headers
4. **Date Ranges**: Limit analysis to relevant time periods
5. **Supplier Names**: Standardize supplier naming conventions

## Next Steps

- **[Stock Analysis Tutorial](./stock-analysis.md)** - Learn about stock market analysis
- **[Advanced Features](./advanced-features.md)** - Explore power user features
- **[API Reference](../technical/api-reference.md)** - Complete API documentation
- **[Agent Workflows](../agents/financial-analysis-agents.md)** - Understanding the AI agents

---

**Need Help?** Check our [Troubleshooting Guide](../reference/troubleshooting.md) or [FAQ](../reference/faq.md)
