"""
Unit tests for the dashboard agent.
"""

import pytest
import pandas as pd
import sys
import os
from unittest.mock import Mock, patch
import json

# Add the backend to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.agents.dashboard_agent import (
    create_supplier_chart, create_product_chart, 
    create_total_summary_chart, create_trend_chart,
    DashboardAgent, generate_kpi_dashboard
)


@pytest.fixture
def sample_financial_data():
    """Create sample financial data for testing."""
    return pd.DataFrame({
        'supplier': ['Supplier A', 'Supplier B', 'Supplier C', 'Supplier A', 'Supplier B'],
        'product': ['Product 1', 'Product 2', 'Product 3', 'Product 2', 'Product 1'],
        'gross_amount': [1000, 1500, 800, 1200, 900],
        'date': ['2024-01-15', '2024-01-20', '2024-02-10', '2024-02-15', '2024-03-05'],
        'voucher': ['V001', 'V002', 'V003', 'V004', 'V005']
    })


class TestDashboardTools:
    """Test the individual dashboard tools."""
    
    def test_create_supplier_chart_success(self, sample_financial_data):
        """Test successful supplier chart creation."""
        data_json = sample_financial_data.to_json()
        
        result = create_supplier_chart.invoke({
            "data": data_json,
            "kpi": "income",
            "chart_type": "bar"
        })
        
        # Should return valid JSON
        chart_data = json.loads(result)
        assert "error" not in chart_data
        assert "data" in chart_data or "layout" in chart_data
    
    def test_create_supplier_chart_invalid_kpi(self, sample_financial_data):
        """Test supplier chart with invalid KPI."""
        data_json = sample_financial_data.to_json()
        
        result = create_supplier_chart.invoke({
            "data": data_json,
            "kpi": "invalid_kpi",
            "chart_type": "bar"
        })
        
        chart_data = json.loads(result)
        assert "error" in chart_data
    
    def test_create_product_chart_success(self, sample_financial_data):
        """Test successful product chart creation."""
        data_json = sample_financial_data.to_json()
        
        result = create_product_chart.invoke({
            "data": data_json,
            "kpi": "income",
            "chart_type": "bar"
        })
        
        chart_data = json.loads(result)
        assert "error" not in chart_data
    
    def test_create_total_summary_chart(self, sample_financial_data):
        """Test total summary chart creation."""
        data_json = sample_financial_data.to_json()
        
        result = create_total_summary_chart.invoke({
            "data": data_json,
            "kpi": "income"
        })
        
        chart_data = json.loads(result)
        assert "error" not in chart_data
    
    def test_create_trend_chart_success(self, sample_financial_data):
        """Test trend chart creation with date data."""
        data_json = sample_financial_data.to_json()
        
        result = create_trend_chart.invoke({
            "data": data_json,
            "kpi": "income"
        })
        
        chart_data = json.loads(result)
        assert "error" not in chart_data


class TestDashboardAgent:
    """Test the main dashboard agent with batch processing."""

    @patch('app.agents.dashboard_agent.ChatGroq')
    def test_dashboard_agent_initialization(self, mock_groq):
        """Test dashboard agent initialization."""
        agent = DashboardAgent()

        assert agent.llm is not None
        assert len(agent.tools) == 4
        assert agent.agent is not None
        assert agent.agent_executor is not None
        assert agent.agent_executor.max_iterations == 3  # Check batch processing limit
    
    def test_extract_kpi_income(self):
        """Test KPI extraction for income queries."""
        agent = DashboardAgent()
        
        test_cases = [
            ("show me income", "income"),
            ("revenue dashboard", "income"),
            ("sales analysis", "income"),
            ("profit margins", "profit"),
            ("cost breakdown", "cost"),
            ("volume metrics", "volume"),
            ("random query", "income")  # default
        ]
        
        for query, expected_kpi in test_cases:
            result = agent._extract_kpi(query)
            assert result == expected_kpi
    
    @patch('app.agents.dashboard_agent.dashboard_agent')
    def test_generate_kpi_dashboard_function(self, mock_agent, sample_financial_data):
        """Test the main generate_kpi_dashboard function with batch processing."""
        # Mock the agent's generate_dashboard method
        mock_agent.generate_dashboard.return_value = {
            "success": True,
            "kpi": "income",
            "charts": {"supplier_chart": "mock_chart_data"},
            "agent_insights": ["Batch 1: Analysis complete"],
            "final_summary": "Dashboard generated successfully",
            "charts_generated": 4,
            "batches_processed": 3,
            "successful_batches": 3,
            "total_records_analyzed": 5
        }

        result = generate_kpi_dashboard(sample_financial_data, "income dashboard")

        assert result["success"] is True
        assert result["kpi"] == "income"
        assert result["charts_generated"] == 4
        assert "agent_insights" in result
        assert "final_summary" in result
        assert result["batches_processed"] == 3

    def test_batch_processing_logic(self, sample_financial_data):
        """Test the batch processing logic."""
        agent = DashboardAgent()

        # Test batch size calculation
        batch_size = 20
        expected_batches = min(3, (len(sample_financial_data) + batch_size - 1) // batch_size)

        # For our sample data (5 records), we should get 1 batch
        assert expected_batches == 1

        # Test with larger dataset
        large_data = pd.concat([sample_financial_data] * 20)  # 100 records
        expected_batches_large = min(3, (len(large_data) + batch_size - 1) // batch_size)
        assert expected_batches_large == 3  # Should be capped at 3


class TestIntegration:
    """Integration tests for the dashboard functionality."""
    
    def test_data_processing_pipeline(self, sample_financial_data):
        """Test the complete data processing pipeline."""
        # Test that data can be converted to JSON and back
        data_json = sample_financial_data.to_json()
        reconstructed_df = pd.read_json(data_json)
        
        assert len(reconstructed_df) == len(sample_financial_data)
        assert list(reconstructed_df.columns) == list(sample_financial_data.columns)
    
    def test_kpi_mapping_logic(self, sample_financial_data):
        """Test KPI to column mapping logic."""
        # Test that the KPI mapping works correctly
        kpi_mapping = {
            'income': ['gross_amount', 'total_amount', 'amount'],
            'profit': ['margin_amount', 'profit', 'net_amount'],
            'cost': ['cost_amount', 'cost', 'expense'],
            'volume': ['quantity', 'units', 'count']
        }
        
        # For our sample data, 'income' should map to 'gross_amount'
        target_column = None
        for mapped_col in kpi_mapping.get('income', []):
            if mapped_col in sample_financial_data.columns:
                target_column = mapped_col
                break
        
        assert target_column == 'gross_amount'


if __name__ == "__main__":
    pytest.main([__file__])
