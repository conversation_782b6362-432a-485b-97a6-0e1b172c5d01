"""
Configuration settings for the financial analysis application.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration constants
GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")
MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "50"))
BATCH_PROCESSING_THRESHOLD = int(os.getenv("BATCH_PROCESSING_THRESHOLD", "500"))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "200"))

# LLM Models
GROQ_MODEL_FAST = os.getenv("GROQ_MODEL_FAST", "llama-3.1-8b-instant")
GROQ_MODEL_ANALYSIS = os.getenv("GROQ_MODEL_ANALYSIS", "llama3-70b-8192")
GROQ_MODEL_REPORT = os.getenv("GROQ_MODEL_REPORT", "gemma2-9b-it")

# Analysis Parameters
DEFAULT_COST_PERCENTAGE = float(os.getenv("DEFAULT_COST_PERCENTAGE", "70.0"))
DEFAULT_LOW_MARGIN_THRESHOLD = float(os.getenv("DEFAULT_LOW_MARGIN_THRESHOLD", "10.0"))

# Application Settings
DEBUG = os.getenv("DEBUG", "False").lower() == "true"


class Settings:
    """Application settings for backward compatibility."""

    def __init__(self):
        self.groq_api_key = GROQ_API_KEY
        self.max_file_size_mb = MAX_FILE_SIZE_MB
        self.batch_processing_threshold = BATCH_PROCESSING_THRESHOLD
        self.batch_size = BATCH_SIZE
        self.groq_model_fast = GROQ_MODEL_FAST
        self.groq_model_analysis = GROQ_MODEL_ANALYSIS
        self.groq_model_report = GROQ_MODEL_REPORT
        self.default_cost_percentage = DEFAULT_COST_PERCENTAGE
        self.default_low_margin_threshold = DEFAULT_LOW_MARGIN_THRESHOLD
        self.debug = DEBUG


# Global settings instance
settings = Settings()


def get_processing_method(record_count: int) -> str:
    """
    Determine processing method based on record count.

    Args:
        record_count: Number of records in the dataset

    Returns:
        Processing method: 'ai_full', 'ai_simple', or 'batch'
    """
    if record_count <= 100:
        return 'ai_full'  # Full AI analysis with all agents
    elif record_count <= BATCH_PROCESSING_THRESHOLD:
        return 'ai_simple'  # Simplified AI analysis
    else:
        return 'batch'  # Batch processing without AI


def get_batch_info(record_count: int) -> dict:
    """
    Get batch processing information.

    Args:
        record_count: Total number of records

    Returns:
        Dictionary with batch information
    """
    if record_count <= BATCH_PROCESSING_THRESHOLD:
        return {
            'needs_batching': False,
            'batch_count': 1,
            'batch_size': record_count,
            'processing_method': get_processing_method(record_count)
        }

    batch_count = (record_count + BATCH_SIZE - 1) // BATCH_SIZE

    return {
        'needs_batching': True,
        'batch_count': batch_count,
        'batch_size': BATCH_SIZE,
        'processing_method': 'batch',
        'estimated_time_minutes': batch_count * 0.5  # Rough estimate
    }


def get_model_for_task(task: str) -> str:
    """
    Get appropriate model for specific task.

    Args:
        task: Task type ('validation', 'analysis', 'report')

    Returns:
        Model name
    """
    model_mapping = {
        'validation': GROQ_MODEL_FAST,
        'analysis': GROQ_MODEL_ANALYSIS,
        'report': GROQ_MODEL_REPORT
    }

    return model_mapping.get(task, GROQ_MODEL_FAST)


# Export commonly used settings
MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024  # Convert to bytes
BATCH_THRESHOLD = BATCH_PROCESSING_THRESHOLD
