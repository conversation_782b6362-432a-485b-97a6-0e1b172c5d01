"""
FastAPI backend for financial analysis project.
Provides REST API endpoints for Excel file upload and analysis.
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import os
from typing import Optional
import sys

# Add the project root to Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.services.financial_analyzer import FinancialAnalyzer
from app.config import settings, MAX_FILE_SIZE, get_batch_info
from shared.schemas import AnalysisResponse, FinancialAnalysisResult

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Financial Analysis API",
    description="API for analyzing financial data from Excel files using LangChain pandas agents with batch processing for large datasets",
    version="1.0.0"
)

# Configure CORS for Streamlit frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8501", "http://127.0.0.1:8501"],  # Streamlit default ports
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
financial_analyzer = FinancialAnalyzer()


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Financial Analysis API",
        "version": "1.0.0",
        "endpoints": {
            "analyze": "POST /analyze - Upload Excel file for financial analysis",
            "health": "GET /health - Health check endpoint"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "financial-analysis-api"}


@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_financial_data(
    file: UploadFile = File(..., description="Excel file with financial data"),
    assume_cost_percentage: Optional[float] = Form(70.0, description="Percentage to assume as cost when cost data is missing"),
    low_margin_threshold: Optional[float] = Form(10.0, description="Threshold percentage below which margins are considered low")
):
    """
    Analyze financial data from uploaded Excel file.
    
    This endpoint processes Excel files containing financial transaction data and performs:
    - Total gross amount calculations per supplier and voucher
    - Margin analysis with configurable cost assumptions
    - Identification of low-margin and negative-margin transactions
    - Month-over-month profitability trend analysis
    
    Args:
        file: Excel file containing financial data
        assume_cost_percentage: Percentage of gross amount to assume as cost when missing (default: 70%)
        low_margin_threshold: Threshold below which margins are flagged as low (default: 10%)
    
    Returns:
        AnalysisResponse with complete financial analysis results
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file extension
        allowed_extensions = ['.xlsx', '.xls', '.csv']
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Read file content
        file_content = await file.read()
        
        # Check file size
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file provided")
        
        # Prepare analysis configuration
        config = {
            "assume_cost_percentage": assume_cost_percentage,
            "low_margin_threshold": low_margin_threshold
        }
        
        logger.info(f"Starting analysis for file: {file.filename}")
        
        # Perform financial analysis
        analysis_result = financial_analyzer.analyze_file(
            file_content=file_content,
            filename=file.filename,
            config=config
        )
        
        logger.info(f"Analysis completed for file: {file.filename}")
        
        return AnalysisResponse(
            success=True,
            message="Financial analysis completed successfully",
            data=analysis_result
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error analyzing file {file.filename}: {str(e)}")
        return AnalysisResponse(
            success=False,
            message="Analysis failed",
            error_details={
                "error": str(e),
                "type": type(e).__name__
            }
        )


@app.post("/analyze-batch")
async def analyze_multiple_files(
    files: list[UploadFile] = File(..., description="Multiple Excel files for batch analysis"),
    assume_cost_percentage: Optional[float] = Form(70.0),
    low_margin_threshold: Optional[float] = Form(10.0)
):
    """
    Analyze multiple financial data files in batch.
    
    Args:
        files: List of Excel files containing financial data
        assume_cost_percentage: Percentage to assume as cost when missing
        low_margin_threshold: Threshold for low margin identification
    
    Returns:
        List of analysis results for each file
    """
    if len(files) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 files allowed per batch")
    
    results = []
    config = {
        "assume_cost_percentage": assume_cost_percentage,
        "low_margin_threshold": low_margin_threshold
    }
    
    for file in files:
        try:
            file_content = await file.read()
            
            if len(file_content) > MAX_FILE_SIZE:
                results.append(AnalysisResponse(
                    success=False,
                    message=f"File {file.filename} too large",
                    error_details={"error": "File size exceeds limit"}
                ))
                continue
            
            analysis_result = financial_analyzer.analyze_file(
                file_content=file_content,
                filename=file.filename,
                config=config
            )
            
            results.append(AnalysisResponse(
                success=True,
                message=f"Analysis completed for {file.filename}",
                data=analysis_result
            ))
            
        except Exception as e:
            logger.error(f"Error analyzing file {file.filename}: {str(e)}")
            results.append(AnalysisResponse(
                success=False,
                message=f"Analysis failed for {file.filename}",
                error_details={
                    "error": str(e),
                    "type": type(e).__name__
                }
            ))
    
    return {"results": results}


@app.get("/analysis-info")
async def get_analysis_info():
    """Get information about the analysis capabilities."""
    return {
        "supported_formats": [".xlsx", ".xls", ".csv"],
        "max_file_size_mb": MAX_FILE_SIZE // (1024 * 1024),
        "processing_methods": {
            "small_files": {
                "threshold": "≤ 100 records",
                "method": "Full AI analysis with all LangChain agents",
                "estimated_time": "1-2 minutes"
            },
            "medium_files": {
                "threshold": "101-500 records",
                "method": "Simplified AI analysis",
                "estimated_time": "2-3 minutes"
            },
            "large_files": {
                "threshold": "> 500 records",
                "method": "Batch processing (200 records per batch)",
                "estimated_time": "3-10 minutes depending on size"
            }
        },
        "analysis_features": [
            "Total gross amount per supplier and voucher",
            "Margin calculations with configurable cost assumptions",
            "Low-margin transaction identification",
            "Negative-margin transaction highlighting",
            "Month-over-month profitability trends",
            "Supplier performance analysis",
            "Automatic batch processing for large datasets"
        ],
        "required_columns": [
            "supplier (or similar: vendor, proveedor)",
            "voucher (or similar: invoice, factura, comprobante)",
            "gross_amount (or similar: amount, total, importe)"
        ],
        "optional_columns": [
            "cost (or similar: cost_amount, costo)",
            "date (or similar: transaction_date, fecha)"
        ],
        "default_parameters": {
            "assume_cost_percentage": 70.0,
            "low_margin_threshold": 10.0
        },
        "batch_processing": {
            "threshold_records": settings.batch_processing_threshold,
            "batch_size": settings.batch_size,
            "benefits": [
                "Handles large datasets without API limits",
                "Reliable processing for files with thousands of records",
                "Optimized performance and memory usage"
            ]
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
