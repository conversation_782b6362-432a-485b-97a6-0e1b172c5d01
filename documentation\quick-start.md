# Quick Start Guide

Get up and running with the Arroyo Consulting Financial Analysis Platform in 5 minutes!

## Prerequisites

- Python 3.8 or higher
- Git
- A Groq API key (free at [console.groq.com](https://console.groq.com))

## 🚀 Quick Setup

### 1. Clone and Navigate
```bash
cd "c:\Users\<USER>\OneDrive\Desktop\ArroyoConsulting\Flujos,n8n"
```

### 2. Set Up Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

#### For Financial Analysis Project:
```bash
cd financial_analysis_project

# Backend dependencies
cd backend
pip install -r requirements.txt
cd ..

# Frontend dependencies
cd frontend
pip install -r requirements.txt
cd ..
```

#### For Stock Analysis:
```bash
# Install additional dependencies for stock analysis
pip install yfinance langchain-community duckduckgo-search
```

### 4. Configure Environment Variables
Create a `.env` file in the `financial_analysis_project/backend` directory:
```bash
GROQ_API_KEY=your_groq_api_key_here
```

## 🎯 Quick Test Runs

### Test 1: Stock Analysis (ejemplo.py)
```bash
# From the root directory
python ejemplo.py
```

**Expected Output**: A comprehensive financial report for Google (GOOGL) including:
- Technical analysis (7-day average, 200-period moving average)
- Recent news analysis
- Investment recommendations

### Test 2: Financial Analysis API
```bash
# Start the backend server
cd financial_analysis_project/backend
uvicorn app.main:app --reload --port 8000
```

In another terminal:
```bash
# Test with sample data
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@financial_analysis_project/sample_data/sample_financial_data.xlsx"
```

### Test 3: Streamlit Frontend
```bash
# Start the frontend
cd financial_analysis_project/frontend
streamlit run streamlit_app.py
```

Visit `http://localhost:8501` and upload an Excel file to see the analysis in action.

## 📊 What You'll See

### Stock Analysis Output
```
========== REPORTE FINAL ==========

INFORME FINANCIERO: GOOGL (Alphabet Inc.)

Introducción:
Este informe analiza el desempeño de GOOGL hasta el 2025-01-08...

Análisis Financiero:
- Promedio de cierre (7 días): $XXX.XX
- Media móvil (200 períodos): $XXX.XX
- Tendencia: Alcista/Bajista/Neutral

Noticias del Mercado:
[Recent news headlines and market impact analysis]

Conclusión:
Basado en el análisis técnico y las noticias recientes...
```

### Financial Analysis Output
```json
{
  "supplier_analysis": "Top suppliers by gross amount...",
  "voucher_analysis": "Voucher-level transaction analysis...",
  "margin_analysis": "Profit margin calculations and risk assessment...",
  "trend_analysis": "Month-over-month profitability trends...",
  "final_report": "Executive summary with actionable insights..."
}
```

## 🔧 Common Issues & Solutions

### Issue: "ModuleNotFoundError"
**Solution**: Make sure you're in the correct directory and virtual environment is activated:
```bash
# Check current directory
pwd

# Activate virtual environment
venv\Scripts\activate  # Windows
source venv/bin/activate  # macOS/Linux

# Reinstall dependencies
pip install -r requirements.txt
```

### Issue: "GROQ_API_KEY not found"
**Solution**: Create the `.env` file with your API key:
```bash
# In financial_analysis_project/backend/.env
GROQ_API_KEY=your_actual_api_key_here
```

### Issue: "Port already in use"
**Solution**: Use a different port:
```bash
uvicorn app.main:app --reload --port 8001
```

### Issue: Excel file not processing
**Solution**: Ensure your Excel file has the required columns:
- Supplier information
- Voucher/transaction IDs
- Amount/value columns
- Date columns

## 📁 Sample Data

Use the provided sample files to test the system:

### Financial Analysis
- `financial_analysis_project/sample_data/sample_financial_data.xlsx`
- `financial_analysis_project/sample_data/comprehensive_financial_data.xlsx`
- `financial_analysis_project/sample_data/datos_financieros_es.xlsx`

### Stock Analysis
The stock analysis system automatically fetches data from Yahoo Finance, so no sample files are needed.

## 🎯 Next Steps

Now that you have the system running, explore these features:

### 1. Try Different Stocks
Modify `ejemplo.py` to analyze different stocks:
```python
if __name__ == "__main__":
    resultado = correr_modelo("quiero un informe de Apple")  # or Tesla, Microsoft, etc.
```

### 2. Upload Your Own Financial Data
Use the Streamlit interface to upload your own Excel files and see the analysis.

### 3. Explore the API
Test different endpoints:
```bash
# Health check
curl http://localhost:8000/health

# Upload and analyze
curl -X POST "http://localhost:8000/analyze" -F "file=@your_file.xlsx"
```

### 4. Customize Analysis
Modify the agent prompts in:
- `financial_analysis_project/backend/app/agents/pandas_agents.py`
- `ejemplo.py`

## 📚 Learn More

- **[Financial Analysis Tutorial](./tutorials/financial-analysis.md)** - Detailed Excel analysis guide
- **[Stock Analysis Tutorial](./tutorials/stock-analysis.md)** - Stock market analysis walkthrough
- **[Agent Workflows](./agents/financial-analysis-agents.md)** - Understanding the AI agents
- **[API Reference](./technical/api-reference.md)** - Complete API documentation

## 🆘 Need Help?

- **[Troubleshooting Guide](./reference/troubleshooting.md)** - Common issues and solutions
- **[FAQ](./reference/faq.md)** - Frequently asked questions
- **[Development Setup](./development/setup.md)** - Advanced development configuration

---

**Congratulations!** 🎉 You now have a fully functional AI-powered financial analysis platform running locally.
