["tests/test_agents.py::TestErrorHandling::test_empty_dataframe_handling", "tests/test_agents.py::TestErrorHandling::test_missing_columns_handling", "tests/test_agents.py::TestExcelProcessorAgent::test_process_method", "tests/test_agents.py::TestFinancialReportGenerator::test_generate_report", "tests/test_agents.py::TestGrossAmountAnalyzer::test_execute_method", "tests/test_agents.py::TestMarginCalculator::test_execute_with_cost_assumption", "tests/test_agents.py::TestTrendAnalyzer::test_execute_trend_analysis", "tests/test_agents.py::TestWorkflowIntegration::test_run_financial_analysis_workflow", "tests/test_agents.py::TestWorkflowIntegration::test_workflow_state_management", "tests/test_backend.py::TestAPI::test_analysis_info_endpoint", "tests/test_backend.py::TestAPI::test_analyze_endpoint_invalid_file_type", "tests/test_backend.py::TestAPI::test_analyze_endpoint_no_file", "tests/test_backend.py::TestAPI::test_health_endpoint", "tests/test_backend.py::TestAPI::test_root_endpoint", "tests/test_backend.py::TestDataValidation::test_duplicate_vouchers", "tests/test_backend.py::TestDataValidation::test_empty_dataframe", "tests/test_backend.py::TestDataValidation::test_negative_amounts", "tests/test_backend.py::TestExcelProcessor::test_column_mapping", "tests/test_backend.py::TestExcelProcessor::test_data_cleaning", "tests/test_backend.py::TestExcelProcessor::test_get_data_summary", "tests/test_backend.py::TestExcelProcessor::test_missing_required_columns", "tests/test_backend.py::TestFinancialAnalyzer::test_margin_calculation", "tests/test_backend.py::TestFinancialAnalyzer::test_monthly_trends", "tests/test_backend.py::TestFinancialAnalyzer::test_supplier_summaries", "tests/test_backend.py::TestFinancialAnalyzer::test_voucher_summaries", "tests/test_dashboard_agent.py::TestDashboardAgent::test_dashboard_agent_initialization", "tests/test_dashboard_agent.py::TestDashboardAgent::test_extract_kpi_income", "tests/test_dashboard_agent.py::TestDashboardAgent::test_generate_kpi_dashboard_function", "tests/test_dashboard_agent.py::TestDashboardTools::test_create_product_chart_success", "tests/test_dashboard_agent.py::TestDashboardTools::test_create_supplier_chart_invalid_kpi", "tests/test_dashboard_agent.py::TestDashboardTools::test_create_supplier_chart_success", "tests/test_dashboard_agent.py::TestDashboardTools::test_create_total_summary_chart", "tests/test_dashboard_agent.py::TestDashboardTools::test_create_trend_chart_success", "tests/test_dashboard_agent.py::TestIntegration::test_data_processing_pipeline", "tests/test_dashboard_agent.py::TestIntegration::test_kpi_mapping_logic"]