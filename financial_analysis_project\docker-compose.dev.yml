version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financial-analysis-backend-dev
    ports:
      - "8000:8000"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - DEBUG=True
      - MAX_FILE_SIZE_MB=50
      - BATCH_PROCESSING_THRESHOLD=500
      - BATCH_SIZE=200
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - backend_data_dev:/app/data
    restart: unless-stopped
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    networks:
      - financial-analysis-network-dev

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: financial-analysis-frontend-dev
    ports:
      - "8501:8501"
    environment:
      - BACKEND_URL=http://backend:8000
    volumes:
      - ./frontend:/app
    restart: unless-stopped
    depends_on:
      - backend
    command: ["streamlit", "run", "streamlit_app.py", "--server.address", "0.0.0.0", "--server.port", "8501", "--server.headless", "true", "--server.runOnSave", "true"]
    networks:
      - financial-analysis-network-dev

volumes:
  backend_data_dev:
    driver: local

networks:
  financial-analysis-network-dev:
    driver: bridge
