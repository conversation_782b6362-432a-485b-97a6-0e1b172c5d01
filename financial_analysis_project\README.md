# Financial Analysis Project

A comprehensive financial analysis tool that processes Excel files and calculates various financial metrics using LangChain pandas agents, with a Streamlit frontend and FastAPI backend.

## Features

- **Excel File Processing**: Upload and analyze financial data from Excel files
- **Financial Calculations**:
  - Total gross amount per supplier and per voucher
  - Margin calculations (with cost data or assumptions)
  - Transaction highlighting for margins < 10% or negative
  - Supplier-level month-over-month profitability trends
- **LangChain Integration**: Uses pandas agents for intelligent data analysis
- **Modern Architecture**: FastAPI backend with Streamlit frontend
- **REST API**: POST endpoints for programmatic access

## Project Structure

```
financial_analysis_project/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── main.py         # FastAPI application
│   │   ├── models/         # Pydantic models
│   │   ├── services/       # Business logic
│   │   ├── agents/         # LangChain pandas agents
│   │   └── utils/          # Helper functions
│   └── requirements.txt
├── frontend/               # Streamlit frontend
│   ├── streamlit_app.py   # Main Streamlit app
│   ├── components/        # UI components
│   └── requirements.txt
├── shared/                # Shared schemas
├── tests/                 # Test files
├── sample_data/          # Sample Excel files
└── README.md
```

## Installation

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
```

### Frontend Setup
```bash
cd frontend
pip install -r requirements.txt
```

## Environment Variables

Create a `.env` file in the backend directory:
```
GROQ_API_KEY=your_groq_api_key_here
```

## Usage

### Start Backend
```bash
cd backend
uvicorn app.main:app --reload --port 8000
```

### Start Frontend
```bash
cd frontend
streamlit run streamlit_app.py
```

### API Usage
```bash
curl -X POST "http://localhost:8000/analyze" \
  -F "file=@your_financial_data.xlsx"
```

## Analysis Features

1. **Gross Amount Analysis**: Calculates totals per supplier and voucher
2. **Margin Analysis**: Computes profit margins and identifies low-margin transactions
3. **Trend Analysis**: Month-over-month profitability trends by supplier
4. **Data Validation**: Comprehensive error handling and data validation

## Technology Stack

- **Backend**: FastAPI, LangChain, Pandas
- **Frontend**: Streamlit
- **AI/ML**: LangChain pandas agents with ChatGroq
- **Data Processing**: Pandas, OpenPyXL
- **API**: RESTful endpoints with file upload support
